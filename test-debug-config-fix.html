<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEBUG_CONFIG修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-results {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <h1>🔧 DEBUG_CONFIG修复测试</h1>
    
    <div class="error-box">
        <h3>🚨 修复的问题</h3>
        <p><strong>错误信息</strong>: <code>ReferenceError: DEBUG_CONFIG is not defined</code></p>
        <p><strong>修复内容</strong>:</p>
        <ul>
            <li>在 <code>mediaDetector.js</code> 中添加 DEBUG_CONFIG 导入</li>
            <li>在 <code>videoSpeedController.js</code> 中添加 DEBUG_CONFIG 导入</li>
            <li>确保所有使用 DEBUG_CONFIG 的模块都正确导入</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🔍 导入检查测试</h3>
        <p>检查所有模块的DEBUG_CONFIG导入是否正确</p>
        
        <button onclick="testImports()">检查导入</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div class="test-results" id="importResults">
            点击"检查导入"开始测试...
        </div>
    </div>

    <div class="test-section">
        <h3>🎯 插件初始化测试</h3>
        <p>测试插件是否能正常初始化而不出现DEBUG_CONFIG错误</p>
        
        <button onclick="testInitialization()">测试初始化</button>
        <button onclick="testModuleStatus()">检查模块状态</button>
        
        <div class="test-results" id="initResults">
            点击按钮开始测试...
        </div>
    </div>

    <div class="test-section">
        <h3>📊 错误监控</h3>
        <p>监控页面上的DEBUG_CONFIG相关错误</p>
        
        <button onclick="startErrorMonitoring()">开始监控</button>
        <button onclick="stopErrorMonitoring()">停止监控</button>
        
        <div class="test-results" id="errorMonitoring">
            错误监控未启动...
        </div>
    </div>

    <script>
        let errorMonitoring = false;
        let debugConfigErrors = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            return `[${timestamp}] ${prefix} ${message}\n`;
        }
        
        function clearResults() {
            document.getElementById('importResults').textContent = '测试结果已清除';
        }
        
        function testImports() {
            const resultsDiv = document.getElementById('importResults');
            let output = log('开始检查DEBUG_CONFIG导入...');
            resultsDiv.textContent = output;
            
            // 检查插件是否加载
            if (window.videoSpeedController) {
                output += log('插件已加载', 'success');
                
                // 检查各个模块
                const modules = [
                    'mediaDetector',
                    'shortcutManager', 
                    'playbackController',
                    'uiManager'
                ];
                
                modules.forEach(moduleName => {
                    if (window.videoSpeedController[moduleName]) {
                        output += log(`${moduleName}: 已初始化`, 'success');
                    } else {
                        output += log(`${moduleName}: 未初始化`, 'error');
                    }
                });
                
                // 检查DEBUG_CONFIG是否可访问
                try {
                    // 尝试通过插件访问DEBUG_CONFIG
                    if (window.videoSpeedController.DEBUG_CONFIG !== undefined) {
                        output += log('DEBUG_CONFIG: 可通过插件访问', 'success');
                    } else {
                        output += log('DEBUG_CONFIG: 无法通过插件访问', 'warning');
                    }
                } catch (error) {
                    output += log(`DEBUG_CONFIG访问测试失败: ${error.message}`, 'error');
                }
                
            } else {
                output += log('插件未加载', 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function testInitialization() {
            const resultsDiv = document.getElementById('initResults');
            let output = log('开始测试插件初始化...');
            resultsDiv.textContent = output;
            
            try {
                if (window.videoSpeedController) {
                    output += log('插件实例存在', 'success');
                    
                    // 检查初始化状态
                    if (window.videoSpeedController.isInitialized) {
                        output += log('插件已初始化', 'success');
                    } else {
                        output += log('插件未完成初始化', 'warning');
                    }
                    
                    // 检查是否有isReady方法
                    if (typeof window.videoSpeedController.isReady === 'function') {
                        if (window.videoSpeedController.isReady()) {
                            output += log('插件已就绪', 'success');
                        } else {
                            output += log('插件未就绪', 'warning');
                        }
                    } else {
                        output += log('isReady方法不存在', 'error');
                    }
                    
                    // 检查降级模式
                    if (window.videoSpeedController.degradedMode) {
                        output += log('运行在降级模式', 'warning');
                    } else {
                        output += log('运行在正常模式', 'success');
                    }
                    
                } else {
                    output += log('插件实例不存在', 'error');
                }
                
            } catch (error) {
                output += log(`初始化测试失败: ${error.message}`, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function testModuleStatus() {
            const resultsDiv = document.getElementById('initResults');
            let output = resultsDiv.textContent + log('\n检查模块详细状态...');
            
            try {
                if (window.videoSpeedController) {
                    const controller = window.videoSpeedController;
                    
                    // 检查mediaDetector
                    if (controller.mediaDetector) {
                        output += log('mediaDetector: 已初始化', 'success');
                        
                        // 尝试调用一个方法来确认模块正常工作
                        try {
                            const mediaElements = controller.mediaDetector.getAllMediaElements();
                            output += log(`mediaDetector: 找到 ${mediaElements.length} 个媒体元素`, 'success');
                        } catch (error) {
                            output += log(`mediaDetector方法调用失败: ${error.message}`, 'error');
                        }
                    } else {
                        output += log('mediaDetector: 未初始化', 'error');
                    }
                    
                    // 检查shortcutManager
                    if (controller.shortcutManager) {
                        output += log('shortcutManager: 已初始化', 'success');
                        
                        if (typeof controller.shortcutManager.registerHandler === 'function') {
                            output += log('shortcutManager.registerHandler: 方法存在', 'success');
                        } else {
                            output += log('shortcutManager.registerHandler: 方法不存在', 'error');
                        }
                    } else {
                        output += log('shortcutManager: 未初始化', 'error');
                    }
                    
                } else {
                    output += log('无法检查模块状态：插件未加载', 'error');
                }
                
            } catch (error) {
                output += log(`模块状态检查失败: ${error.message}`, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function startErrorMonitoring() {
            if (errorMonitoring) return;
            
            errorMonitoring = true;
            debugConfigErrors = [];
            
            const resultsDiv = document.getElementById('errorMonitoring');
            let output = log('开始DEBUG_CONFIG错误监控...');
            resultsDiv.textContent = output;
            
            // 监听所有错误
            window.addEventListener('error', handleError);
            window.addEventListener('unhandledrejection', handleRejection);
            
            function handleError(event) {
                if (!errorMonitoring) return;
                
                const error = event.error || event;
                const message = error.message || event.message || '未知错误';
                
                if (message.includes('DEBUG_CONFIG')) {
                    debugConfigErrors.push({
                        type: 'error',
                        message: message,
                        filename: event.filename,
                        lineno: event.lineno,
                        timestamp: new Date().toISOString()
                    });
                    
                    output += log(`❌ DEBUG_CONFIG错误: ${message} (${event.filename}:${event.lineno})`, 'error');
                    resultsDiv.textContent = output;
                }
            }
            
            function handleRejection(event) {
                if (!errorMonitoring) return;
                
                const reason = String(event.reason || '未知Promise拒绝');
                
                if (reason.includes('DEBUG_CONFIG')) {
                    debugConfigErrors.push({
                        type: 'rejection',
                        reason: reason,
                        timestamp: new Date().toISOString()
                    });
                    
                    output += log(`❌ DEBUG_CONFIG Promise拒绝: ${reason}`, 'error');
                    resultsDiv.textContent = output;
                }
            }
            
            // 5秒后显示初步结果
            setTimeout(() => {
                if (debugConfigErrors.length === 0) {
                    output += log('🎉 5秒内无DEBUG_CONFIG错误，修复成功！', 'success');
                } else {
                    output += log(`⚠️ 检测到 ${debugConfigErrors.length} 个DEBUG_CONFIG错误`, 'warning');
                }
                resultsDiv.textContent = output;
            }, 5000);
        }
        
        function stopErrorMonitoring() {
            errorMonitoring = false;
            const resultsDiv = document.getElementById('errorMonitoring');
            resultsDiv.textContent += log(`错误监控已停止，共检测到 ${debugConfigErrors.length} 个DEBUG_CONFIG错误`);
            
            if (debugConfigErrors.length > 0) {
                resultsDiv.textContent += log('\n错误详情:');
                debugConfigErrors.forEach((error, index) => {
                    resultsDiv.textContent += log(`${index + 1}. ${error.message || error.reason}`);
                });
            }
        }
        
        // 页面加载时自动开始监控和测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                startErrorMonitoring();
                // 2秒后自动运行导入测试
                setTimeout(() => {
                    testImports();
                }, 2000);
                // 4秒后自动运行初始化测试
                setTimeout(() => {
                    testInitialization();
                }, 4000);
            }, 1000);
        });
    </script>
</body>
</html>
