<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试原生视频控制</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
        }
        
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .test-results {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            color: green;
            font-weight: bold;
        }
        
        .error {
            color: red;
            font-weight: bold;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🎬 视频速度控制器 - 原生控制测试</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>这个页面用于测试修改后的插件是否正确地允许原生视频播放器控制工作。</p>
        <ol>
            <li>确保已安装修改后的Chrome扩展</li>
            <li>使用下面的测试视频进行测试</li>
            <li>验证原生快捷键是否正常工作</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🎥 测试视频</h3>
        <video controls id="testVideo">
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
            <p>您的浏览器不支持视频标签。</p>
        </video>
        
        <div class="instructions">
            <h4>🎮 原生控制测试</h4>
            <p>请测试以下原生快捷键是否正常工作：</p>
            <ul>
                <li><strong>空格键</strong>：播放/暂停（应该正常工作）</li>
                <li><strong>← →</strong>：快退/快进（应该正常工作）</li>
                <li><strong>↑ ↓</strong>：音量调节（应该正常工作）</li>
                <li><strong>M</strong>：静音/取消静音（应该正常工作）</li>
                <li><strong>F</strong>：全屏（可能被插件拦截用于网页全屏）</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h4>⚡ 插件快捷键测试</h4>
            <p>请测试以下插件快捷键是否正常工作：</p>
            <ul>
                <li><strong>=</strong>：加速播放（插件功能）</li>
                <li><strong>-</strong>：减速播放（插件功能）</li>
                <li><strong>0</strong>：重置速度（插件功能）</li>
                <li><strong>F</strong>：网页全屏（插件功能）</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3>🧪 自动化测试</h3>
        <button onclick="runTests()">运行测试</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div class="test-results" id="testResults">
            <p>点击"运行测试"开始自动化测试...</p>
        </div>
    </div>

    <script>
        const video = document.getElementById('testVideo');
        const resultsDiv = document.getElementById('testResults');
        
        function log(message, isSuccess = true) {
            const p = document.createElement('p');
            p.className = isSuccess ? 'success' : 'error';
            p.textContent = message;
            resultsDiv.appendChild(p);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '<p>测试结果已清除</p>';
        }
        
        async function runTests() {
            clearResults();
            log('开始测试...');
            
            // 测试1：检查视频元素是否存在
            if (video) {
                log('✓ 视频元素存在');
            } else {
                log('✗ 视频元素不存在', false);
                return;
            }
            
            // 测试2：检查视频是否可以播放
            try {
                await video.play();
                log('✓ 视频可以播放');
                video.pause();
            } catch (error) {
                log('✗ 视频播放失败: ' + error.message, false);
            }
            
            // 测试3：检查插件是否已加载
            if (window.videoSpeedController) {
                log('✓ 插件已加载');
            } else {
                log('⚠ 插件未检测到（这是正常的，如果插件使用模块化加载）');
            }
            
            // 测试4：模拟空格键事件
            log('测试空格键事件...');
            const spaceEvent = new KeyboardEvent('keydown', {
                key: ' ',
                code: 'Space',
                bubbles: true,
                cancelable: true
            });
            
            let eventPrevented = false;
            const originalPreventDefault = spaceEvent.preventDefault;
            spaceEvent.preventDefault = function() {
                eventPrevented = true;
                originalPreventDefault.call(this);
            };
            
            document.dispatchEvent(spaceEvent);
            
            if (eventPrevented) {
                log('⚠ 空格键事件被阻止（可能被插件拦截）', false);
            } else {
                log('✓ 空格键事件未被阻止（原生控制应该正常工作）');
            }
            
            // 测试5：检查插件快捷键
            log('测试插件快捷键...');
            const equalEvent = new KeyboardEvent('keydown', {
                key: '=',
                code: 'Equal',
                bubbles: true,
                cancelable: true
            });
            
            let pluginEventPrevented = false;
            const originalPreventDefault2 = equalEvent.preventDefault;
            equalEvent.preventDefault = function() {
                pluginEventPrevented = true;
                originalPreventDefault2.call(this);
            };
            
            document.dispatchEvent(equalEvent);
            
            if (pluginEventPrevented) {
                log('✓ 插件快捷键（=）被正确处理');
            } else {
                log('⚠ 插件快捷键（=）未被处理');
            }
            
            log('测试完成！');
        }
        
        // 监听键盘事件以显示实时反馈
        document.addEventListener('keydown', function(event) {
            if (event.target === document.body) {
                const keyInfo = `按键: ${event.key} (${event.code})`;
                console.log(keyInfo);
                
                // 显示按键信息
                const keyDisplay = document.getElementById('keyDisplay') || createKeyDisplay();
                keyDisplay.textContent = keyInfo;
                keyDisplay.style.opacity = '1';
                
                setTimeout(() => {
                    keyDisplay.style.opacity = '0.5';
                }, 1000);
            }
        });
        
        function createKeyDisplay() {
            const div = document.createElement('div');
            div.id = 'keyDisplay';
            div.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                z-index: 1000;
                transition: opacity 0.3s;
            `;
            document.body.appendChild(div);
            return div;
        }
    </script>
</body>
</html>
