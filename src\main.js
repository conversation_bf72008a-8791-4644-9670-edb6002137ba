/**
 * 视频速度控制器扩展主入口文件
 * 负责初始化和启动整个扩展
 */

import VideoSpeedController from './videoSpeedController.js';
import { logInfo, logError, createSafeNotification } from './utils.js';

/**
 * 主应用类
 */
class VideoSpeedControllerApp {
    constructor() {
        this.controller = null;
        this.isRunning = false;
    }

    /**
     * 检查是否在Chrome扩展环境中
     * @returns {boolean} 是否在扩展环境中
     */
    isExtensionEnvironment() {
        return typeof chrome !== 'undefined' &&
               chrome.runtime &&
               typeof chrome.runtime.getURL === 'function';
    }

    /**
     * 启动应用
     */
    async start() {
        try {
            const isExtension = this.isExtensionEnvironment();
            const isDegradedMode = window.videoSpeedControllerDegradedMode || !isExtension;

            logInfo('视频速度控制器扩展开始启动', {
                isExtension,
                isDegradedMode,
                location: window.location.href
            });

            // 检查是否已经在运行
            if (this.isRunning) {
                logInfo('扩展已在运行中');
                return;
            }

            // 等待DOM加载完成
            await this.waitForDOM();

            // 创建并初始化控制器
            this.controller = new VideoSpeedController();

            // 如果在降级模式下，预先设置降级标志
            if (isDegradedMode) {
                this.controller.degradedMode = true;
                logInfo('预设降级模式标志');
            }

            await this.controller.init();

            this.isRunning = true;
            logInfo('视频速度控制器扩展启动完成', {
                degradedMode: this.controller.degradedMode
            });

            // 设置全局访问（用于调试）
            if (typeof window !== 'undefined') {
                window.videoSpeedController = this.controller;
            }

        } catch (error) {
            logError('启动视频速度控制器失败', error);
            this.handleStartupError(error);
        }
    }

    /**
     * 等待DOM加载完成
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise} DOM加载完成的Promise
     */
    waitForDOM(timeout = 10000) {
        return new Promise((resolve, reject) => {
            if (document.readyState === 'loading') {
                const timeoutId = setTimeout(() => {
                    reject(new Error('DOM加载超时'));
                }, timeout);

                document.addEventListener('DOMContentLoaded', () => {
                    clearTimeout(timeoutId);
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * 处理启动错误
     * @param {Error} error - 错误对象
     */
    handleStartupError(error) {
        logError('启动错误详情', error);

        // 尝试降级启动
        this.attemptDegradedStart(error);
    }

    /**
     * 尝试降级启动
     * @param {Error} originalError - 原始错误
     */
    async attemptDegradedStart(originalError) {
        try {
            logInfo('尝试降级模式启动');

            // 等待更长时间确保DOM准备就绪
            await this.waitForDOM(5000);

            // 创建简化的控制器
            this.controller = new VideoSpeedController();
            this.controller.degradedMode = true;

            // 尝试初始化
            await this.controller.init();

            if (this.controller.isReady()) {
                this.isRunning = true;
                logInfo('降级模式启动成功');
                this.showDegradedModeNotification();
            } else {
                throw new Error('降级模式启动也失败');
            }

        } catch (degradedError) {
            logError('降级模式启动失败', degradedError);
            this.showStartupError(originalError);
        }
    }

    /**
     * 显示降级模式通知
     */
    showDegradedModeNotification() {
        try {
            const notificationDiv = createSafeNotification(
                '视频速度控制器',
                '以简化模式运行，部分功能可能受限',
                'warning'
            );

            // 调整位置到左侧
            notificationDiv.style.left = '20px';
            notificationDiv.style.right = 'auto';

            if (document.body) {
                document.body.appendChild(notificationDiv);
                setTimeout(() => {
                    if (notificationDiv.parentNode) {
                        notificationDiv.parentNode.removeChild(notificationDiv);
                    }
                }, 3000);
            }
        } catch (error) {
            console.error('显示降级模式通知失败:', error);
        }
    }

    /**
     * 显示启动错误
     * @param {Error} error - 错误对象
     */
    showStartupError(error) {
        try {
            const errorDiv = createSafeNotification(
                '视频速度控制器',
                '启动失败，请刷新页面重试',
                'error'
            );

            // 调整位置到左侧
            errorDiv.style.left = '20px';
            errorDiv.style.right = 'auto';

            if (document.body) {
                document.body.appendChild(errorDiv);
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 5000);
            }
        } catch (error) {
            console.error('显示启动错误失败:', error);
        }
    }

    /**
     * 停止应用
     */
    stop() {
        try {
            logInfo('正在停止视频速度控制器扩展');

            if (this.controller) {
                this.controller.destroy();
                this.controller = null;
            }

            this.isRunning = false;
            logInfo('视频速度控制器扩展已停止');

        } catch (error) {
            logError('停止视频速度控制器失败', error);
        }
    }

    /**
     * 重启应用
     */
    async restart() {
        this.stop();
        await this.start();
    }

    /**
     * 获取应用状态
     * @returns {Object} 应用状态信息
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            isControllerReady: this.controller ? this.controller.isReady() : false,
            currentMedia: this.controller ? this.controller.getCurrentMediaInfo() : null,
            shortcuts: this.controller ? this.controller.getShortcuts() : {},
            errorStats: this.controller ? this.controller.getErrorStats() : null
        };
    }
}

// 创建全局应用实例
const app = new VideoSpeedControllerApp();

// 自动启动应用
app.start();

// 导出应用实例（用于调试和测试）
export default app;

// 在全局作用域中提供访问（用于调试）
if (typeof window !== 'undefined') {
    window.videoSpeedControllerApp = app;
}
