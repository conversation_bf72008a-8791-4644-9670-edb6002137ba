<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube错误修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-results {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        .youtube-link {
            display: inline-block;
            margin: 10px 0;
            padding: 10px 15px;
            background: #ff0000;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 YouTube错误修复测试</h1>
    
    <div class="error-box">
        <h3>🚨 修复的错误</h3>
        <ol>
            <li><strong>TrustedHTML错误</strong>: "This document requires 'TrustedHTML' assignment"</li>
            <li><strong>registerHandler null错误</strong>: "Cannot read properties of null (reading 'registerHandler')"</li>
            <li><strong>innerHTML安全错误</strong>: "Failed to set the 'innerHTML' property"</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🔍 TrustedHTML安全测试</h3>
        <p>测试是否还会出现TrustedHTML相关错误</p>
        
        <button onclick="testTrustedHTML()">测试TrustedHTML安全</button>
        <button onclick="testSafeDOM()">测试安全DOM操作</button>
        
        <div class="test-results" id="trustedHTMLResults">
            点击按钮开始测试...
        </div>
    </div>

    <div class="test-section">
        <h3>🎯 模块初始化测试</h3>
        <p>测试模块是否正确初始化，registerHandler是否可用</p>
        
        <button onclick="testModuleInit()">测试模块初始化</button>
        <button onclick="testRegisterHandler()">测试registerHandler</button>
        
        <div class="test-results" id="moduleResults">
            点击按钮开始测试...
        </div>
    </div>

    <div class="test-section">
        <h3>🎬 YouTube实际测试</h3>
        <p>在修复后访问YouTube进行实际测试</p>
        
        <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" target="_blank" class="youtube-link">
            🎵 测试YouTube视频
        </a>
        
        <div class="code-block">
            在YouTube上检查：
            1. 控制台是否还有TrustedHTML错误
            2. 控制台是否还有registerHandler错误
            3. 插件是否正常加载和工作
            4. 速度控制功能是否正常
        </div>
    </div>

    <div class="test-section">
        <h3>📊 错误监控</h3>
        <button onclick="startErrorMonitoring()">开始错误监控</button>
        <button onclick="stopErrorMonitoring()">停止错误监控</button>
        <button onclick="clearErrorLog()">清除错误日志</button>
        
        <div class="test-results" id="errorLog">
            错误监控未启动...
        </div>
    </div>

    <script>
        let errorMonitoring = false;
        let errorCount = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            return `[${timestamp}] ${prefix} ${message}\n`;
        }
        
        function testTrustedHTML() {
            const resultsDiv = document.getElementById('trustedHTMLResults');
            let output = log('开始TrustedHTML安全测试...');
            resultsDiv.textContent = output;
            
            try {
                // 测试1: 尝试不安全的innerHTML操作
                const testDiv = document.createElement('div');
                testDiv.innerHTML = '<span>测试HTML</span>';
                output += log('innerHTML操作成功（可能不在严格CSP环境）', 'warning');
            } catch (error) {
                if (error.message.includes('TrustedHTML')) {
                    output += log('检测到TrustedHTML限制（正常）', 'success');
                } else {
                    output += log('innerHTML错误: ' + error.message, 'error');
                }
            }
            
            try {
                // 测试2: 使用安全的DOM操作
                const safeDiv = document.createElement('div');
                const span = document.createElement('span');
                span.textContent = '安全的文本内容';
                safeDiv.appendChild(span);
                output += log('安全DOM操作成功', 'success');
            } catch (error) {
                output += log('安全DOM操作失败: ' + error.message, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function testSafeDOM() {
            const resultsDiv = document.getElementById('trustedHTMLResults');
            let output = resultsDiv.textContent + log('测试createSafeElement函数...');
            
            try {
                // 模拟createSafeElement函数
                function createSafeElement(tagName, options = {}) {
                    const element = document.createElement(tagName);
                    if (options.textContent) {
                        element.textContent = options.textContent;
                    }
                    if (options.style) {
                        element.style.cssText = options.style;
                    }
                    return element;
                }
                
                const safeElement = createSafeElement('div', {
                    textContent: '这是安全创建的元素',
                    style: 'color: green; font-weight: bold;'
                });
                
                output += log('createSafeElement函数工作正常', 'success');
            } catch (error) {
                output += log('createSafeElement函数失败: ' + error.message, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function testModuleInit() {
            const resultsDiv = document.getElementById('moduleResults');
            let output = log('开始模块初始化测试...');
            resultsDiv.textContent = output;
            
            // 检查插件是否加载
            if (window.videoSpeedController) {
                output += log('视频速度控制器已加载', 'success');
                
                if (window.videoSpeedController.isReady && window.videoSpeedController.isReady()) {
                    output += log('控制器已就绪', 'success');
                } else {
                    output += log('控制器未就绪', 'warning');
                }
                
                if (window.videoSpeedController.shortcutManager) {
                    output += log('快捷键管理器已初始化', 'success');
                } else {
                    output += log('快捷键管理器未初始化', 'error');
                }
                
                if (window.videoSpeedController.degradedMode) {
                    output += log('运行在降级模式', 'warning');
                } else {
                    output += log('运行在正常模式', 'success');
                }
            } else {
                output += log('视频速度控制器未加载', 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function testRegisterHandler() {
            const resultsDiv = document.getElementById('moduleResults');
            let output = resultsDiv.textContent + log('测试registerHandler方法...');
            
            if (window.videoSpeedController && window.videoSpeedController.shortcutManager) {
                const manager = window.videoSpeedController.shortcutManager;
                
                if (typeof manager.registerHandler === 'function') {
                    output += log('registerHandler方法存在', 'success');
                    
                    try {
                        // 尝试注册一个测试处理器
                        manager.registerHandler(() => {
                            return false; // 测试处理器
                        });
                        output += log('registerHandler调用成功', 'success');
                    } catch (error) {
                        output += log('registerHandler调用失败: ' + error.message, 'error');
                    }
                } else {
                    output += log('registerHandler方法不存在', 'error');
                }
            } else {
                output += log('快捷键管理器不可用', 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function startErrorMonitoring() {
            if (errorMonitoring) return;
            
            errorMonitoring = true;
            errorCount = 0;
            
            const errorLogDiv = document.getElementById('errorLog');
            let output = log('开始错误监控...');
            errorLogDiv.textContent = output;
            
            // 监听全局错误
            window.addEventListener('error', handleError);
            window.addEventListener('unhandledrejection', handleRejection);
            
            function handleError(event) {
                if (!errorMonitoring) return;
                errorCount++;
                
                const error = event.error || event;
                let errorMsg = error.message || '未知错误';
                
                if (errorMsg.includes('TrustedHTML')) {
                    output += log(`TrustedHTML错误 #${errorCount}: ${errorMsg}`, 'error');
                } else if (errorMsg.includes('registerHandler')) {
                    output += log(`registerHandler错误 #${errorCount}: ${errorMsg}`, 'error');
                } else if (errorMsg.includes('innerHTML')) {
                    output += log(`innerHTML错误 #${errorCount}: ${errorMsg}`, 'error');
                } else {
                    output += log(`其他错误 #${errorCount}: ${errorMsg}`, 'warning');
                }
                
                errorLogDiv.textContent = output;
            }
            
            function handleRejection(event) {
                if (!errorMonitoring) return;
                errorCount++;
                
                const reason = event.reason || '未知Promise拒绝';
                output += log(`Promise拒绝 #${errorCount}: ${reason}`, 'error');
                errorLogDiv.textContent = output;
            }
        }
        
        function stopErrorMonitoring() {
            errorMonitoring = false;
            const errorLogDiv = document.getElementById('errorLog');
            errorLogDiv.textContent += log('错误监控已停止');
        }
        
        function clearErrorLog() {
            document.getElementById('errorLog').textContent = '错误日志已清除';
            errorCount = 0;
        }
        
        // 页面加载时自动开始监控
        window.addEventListener('load', () => {
            setTimeout(() => {
                startErrorMonitoring();
            }, 1000);
        });
    </script>
</body>
</html>
