/**
 * 独立测试版本 - 不使用ES模块
 * 用于在file://协议下测试Chrome扩展修复
 */

(function() {
    'use strict';

    // 防止重复加载
    if (window.videoSpeedControllerStandalone) {
        return;
    }

    console.log('启动视频速度控制器独立测试版本');

    // 简化的配置
    const DEFAULT_SHORTCUTS = {
        increase: '=',
        decrease: '-',
        reset: '0'
    };

    // 简化的日志函数
    function logInfo(message, data) {
        console.log('[VSC Info]', message, data || '');
    }

    function logError(message, error) {
        console.error('[VSC Error]', message, error);
    }

    function logDebug(message, data) {
        console.log('[VSC Debug]', message, data || '');
    }

    // 检查API支持
    function checkAPISupport(apiName) {
        try {
            switch (apiName) {
                case 'MutationObserver':
                    return typeof MutationObserver !== 'undefined';
                case 'WeakMap':
                    return typeof WeakMap !== 'undefined';
                case 'chrome.storage':
                    return typeof chrome !== 'undefined' && 
                           chrome.storage && 
                           chrome.storage.sync;
                default:
                    return false;
            }
        } catch (error) {
            logError(`检查API支持失败: ${apiName}`, error);
            return false;
        }
    }

    // 简化的媒体检测器
    class SimpleMediaDetector {
        constructor() {
            this.currentMedia = null;
            this.mediaElements = new Set();
        }

        init() {
            this.scanForMedia();
            this.setupMutationObserver();
            logInfo('媒体检测器初始化完成');
        }

        scanForMedia() {
            const videos = document.querySelectorAll('video');
            const audios = document.querySelectorAll('audio');
            
            [...videos, ...audios].forEach(media => {
                this.mediaElements.add(media);
            });

            logDebug(`发现 ${this.mediaElements.size} 个媒体元素`);
        }

        setupMutationObserver() {
            if (!checkAPISupport('MutationObserver')) {
                logError('MutationObserver不可用');
                return;
            }

            const observer = new MutationObserver(() => {
                this.scanForMedia();
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        getCurrentMedia() {
            // 返回第一个找到的媒体元素
            return this.mediaElements.values().next().value || null;
        }
    }

    // 简化的播放控制器
    class SimplePlaybackController {
        constructor() {
            this.currentSpeed = 1.0;
            this.speedStep = 0.1;
        }

        handlePlayback(media, action) {
            if (!media) {
                logDebug('没有可用的媒体元素');
                return false;
            }

            try {
                switch (action) {
                    case 'increase':
                        return this.increaseSpeed(media);
                    case 'decrease':
                        return this.decreaseSpeed(media);
                    case 'reset':
                        return this.resetSpeed(media);
                    default:
                        logDebug('未知的播放控制动作', action);
                        return false;
                }
            } catch (error) {
                logError('播放控制失败', error);
                return false;
            }
        }

        increaseSpeed(media) {
            const newSpeed = Math.min(4.0, media.playbackRate + this.speedStep);
            media.playbackRate = newSpeed;
            this.currentSpeed = newSpeed;
            logInfo(`播放速度增加到 ${newSpeed.toFixed(1)}x`);
            return true;
        }

        decreaseSpeed(media) {
            const newSpeed = Math.max(0.25, media.playbackRate - this.speedStep);
            media.playbackRate = newSpeed;
            this.currentSpeed = newSpeed;
            logInfo(`播放速度减少到 ${newSpeed.toFixed(1)}x`);
            return true;
        }

        resetSpeed(media) {
            media.playbackRate = 1.0;
            this.currentSpeed = 1.0;
            logInfo('播放速度重置到 1.0x');
            return true;
        }
    }

    // 简化的快捷键管理器
    class SimpleShortcutManager {
        constructor() {
            this.shortcuts = DEFAULT_SHORTCUTS;
            this.handlers = [];
            this.degradedMode = false;
        }

        init() {
            this.setupKeyboardEventListeners();
            logInfo('快捷键管理器初始化完成（降级模式）');
        }

        setupKeyboardEventListeners() {
            const handleKeyDown = (event) => {
                this.handleKeyboardEvent(event);
            };

            window.addEventListener('keydown', handleKeyDown, true);
            document.addEventListener('keydown', handleKeyDown, true);
        }

        handleKeyboardEvent(event) {
            const key = event.key;
            const action = this.findMatchingAction(key);

            if (!action) return;

            // 检查是否应该忽略此事件
            if (this.shouldIgnoreEvent(event)) return;

            // 通知处理器
            const shouldPreventDefault = this.notifyHandlers(action, event);

            if (shouldPreventDefault) {
                event.preventDefault();
                event.stopPropagation();
                logDebug('快捷键已处理', { action, key });
            }
        }

        findMatchingAction(key) {
            return Object.keys(this.shortcuts).find(action =>
                this.shortcuts[action] === key
            );
        }

        shouldIgnoreEvent(event) {
            // 在可编辑区域不拦截快捷键
            if (event.target.isContentEditable ||
                ['INPUT', 'TEXTAREA', 'SELECT'].includes(event.target.tagName)) {
                return true;
            }

            return false;
        }

        notifyHandlers(action, event) {
            let shouldPreventDefault = false;

            for (const handler of this.handlers) {
                try {
                    const result = handler(action, event);
                    if (result === true) {
                        shouldPreventDefault = true;
                    }
                } catch (error) {
                    logError('快捷键处理器执行失败', error);
                }
            }

            return shouldPreventDefault;
        }

        registerHandler(handler) {
            if (typeof handler === 'function') {
                this.handlers.push(handler);
                logDebug('快捷键处理器已注册');
            }
        }
    }

    // 简化的主控制器
    class SimpleVideoSpeedController {
        constructor() {
            this.mediaDetector = null;
            this.playbackController = null;
            this.shortcutManager = null;
            this.degradedMode = true;
            this.isInitialized = false;
        }

        async init() {
            try {
                logInfo('视频速度控制器开始初始化（独立模式）');

                // 初始化各个模块
                this.mediaDetector = new SimpleMediaDetector();
                this.playbackController = new SimplePlaybackController();
                this.shortcutManager = new SimpleShortcutManager();

                // 初始化模块
                this.mediaDetector.init();
                this.shortcutManager.init();

                // 注册快捷键处理器
                this.shortcutManager.registerHandler((action, event) => {
                    return this.handleShortcut(action, event);
                });

                this.isInitialized = true;
                logInfo('视频速度控制器初始化完成（独立模式）');

            } catch (error) {
                logError('视频速度控制器初始化失败', error);
            }
        }

        handleShortcut(action, event) {
            const media = this.mediaDetector.getCurrentMedia();
            if (!media) {
                logDebug('没有找到媒体元素');
                return false;
            }

            return this.playbackController.handlePlayback(media, action);
        }

        isReady() {
            return this.isInitialized;
        }

        getCurrentMediaInfo() {
            const media = this.mediaDetector ? this.mediaDetector.getCurrentMedia() : null;
            if (!media) return null;

            return {
                tagName: media.tagName,
                src: media.src || media.currentSrc,
                duration: media.duration,
                currentTime: media.currentTime,
                playbackRate: media.playbackRate,
                paused: media.paused
            };
        }

        getShortcuts() {
            return this.shortcutManager ? this.shortcutManager.shortcuts : {};
        }
    }

    // 创建并启动控制器
    const controller = new SimpleVideoSpeedController();
    controller.init();

    // 设置全局访问
    window.videoSpeedControllerStandalone = controller;
    window.videoSpeedController = controller; // 兼容性

    logInfo('独立测试版本加载完成');

})();
