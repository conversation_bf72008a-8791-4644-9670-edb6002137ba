<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>logError错误诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-results {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 logError错误诊断</h1>
    
    <div class="error-box">
        <h3>🚨 当前错误</h3>
        <p><strong>错误信息</strong>: <code>Uncaught (in promise) ReferenceError: logError is not defined</code></p>
        <p><strong>可能原因</strong>:</p>
        <ul>
            <li>模块导入顺序问题</li>
            <li>ES模块加载时序问题</li>
            <li>某个文件中缺少logError导入</li>
            <li>循环依赖导致的导入问题</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🔍 模块导入检查</h3>
        <p>检查各个模块的导入状态和logError函数可用性</p>
        
        <button onclick="testModuleImports()">检查模块导入</button>
        <button onclick="testLogErrorFunction()">测试logError函数</button>
        
        <div class="test-results" id="importResults">
            点击按钮开始检查...
        </div>
    </div>

    <div class="test-section">
        <h3>🎯 插件加载状态</h3>
        <p>检查插件的加载状态和各个模块的初始化情况</p>
        
        <button onclick="testPluginStatus()">检查插件状态</button>
        <button onclick="testModuleStatus()">检查模块状态</button>
        
        <div class="test-results" id="statusResults">
            点击按钮开始检查...
        </div>
    </div>

    <div class="test-section">
        <h3>📊 实时错误监控</h3>
        <p>监控页面上的所有JavaScript错误</p>
        
        <button onclick="startErrorCapture()">开始错误捕获</button>
        <button onclick="stopErrorCapture()">停止错误捕获</button>
        <button onclick="clearErrorCapture()">清除错误记录</button>
        
        <div class="test-results" id="errorCapture">
            错误捕获未启动...
        </div>
    </div>

    <div class="test-section">
        <h3>🛠️ 手动测试</h3>
        <p>手动触发可能导致logError错误的操作</p>
        
        <button onclick="triggerShortcutTest()">触发快捷键测试</button>
        <button onclick="triggerModuleInit()">触发模块初始化</button>
        <button onclick="triggerErrorHandling()">触发错误处理</button>
        
        <div class="test-results" id="manualResults">
            手动测试结果将显示在这里...
        </div>
    </div>

    <script>
        let errorCapturing = false;
        let capturedErrors = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            return `[${timestamp}] ${prefix} ${message}\n`;
        }
        
        function testModuleImports() {
            const resultsDiv = document.getElementById('importResults');
            let output = log('开始检查模块导入...');
            resultsDiv.textContent = output;
            
            // 检查全局对象
            const globalChecks = [
                'window.videoSpeedController',
                'window.chrome',
                'window.chrome.runtime',
                'window.chrome.storage'
            ];
            
            globalChecks.forEach(check => {
                try {
                    const value = eval(check);
                    if (value !== undefined) {
                        output += log(`${check}: 存在`, 'success');
                    } else {
                        output += log(`${check}: 不存在`, 'warning');
                    }
                } catch (error) {
                    output += log(`${check}: 检查失败 - ${error.message}`, 'error');
                }
            });
            
            resultsDiv.textContent = output;
        }
        
        function testLogErrorFunction() {
            const resultsDiv = document.getElementById('importResults');
            let output = resultsDiv.textContent + log('测试logError函数...');
            
            try {
                // 尝试访问插件的logError函数
                if (window.videoSpeedController) {
                    output += log('插件已加载', 'success');
                    
                    // 检查是否有utils模块
                    if (window.videoSpeedController.utils) {
                        output += log('utils模块存在', 'success');
                        
                        if (typeof window.videoSpeedController.utils.logError === 'function') {
                            output += log('logError函数存在', 'success');
                        } else {
                            output += log('logError函数不存在', 'error');
                        }
                    } else {
                        output += log('utils模块不存在', 'warning');
                    }
                } else {
                    output += log('插件未加载', 'error');
                }
                
                // 尝试直接调用logError（如果存在）
                if (typeof logError !== 'undefined') {
                    output += log('全局logError函数存在', 'success');
                    logError('测试消息', new Error('测试错误'));
                    output += log('logError调用成功', 'success');
                } else {
                    output += log('全局logError函数不存在', 'warning');
                }
                
            } catch (error) {
                output += log(`logError测试失败: ${error.message}`, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function testPluginStatus() {
            const resultsDiv = document.getElementById('statusResults');
            let output = log('检查插件状态...');
            resultsDiv.textContent = output;
            
            try {
                if (window.videoSpeedController) {
                    const controller = window.videoSpeedController;
                    
                    output += log('插件实例存在', 'success');
                    
                    // 检查初始化状态
                    if (controller.isInitialized) {
                        output += log('插件已初始化', 'success');
                    } else {
                        output += log('插件未初始化', 'warning');
                    }
                    
                    // 检查降级模式
                    if (controller.degradedMode) {
                        output += log('运行在降级模式', 'warning');
                    } else {
                        output += log('运行在正常模式', 'success');
                    }
                    
                    // 检查isReady方法
                    if (typeof controller.isReady === 'function') {
                        if (controller.isReady()) {
                            output += log('插件已就绪', 'success');
                        } else {
                            output += log('插件未就绪', 'warning');
                        }
                    } else {
                        output += log('isReady方法不存在', 'error');
                    }
                    
                } else {
                    output += log('插件实例不存在', 'error');
                }
                
            } catch (error) {
                output += log(`插件状态检查失败: ${error.message}`, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function testModuleStatus() {
            const resultsDiv = document.getElementById('statusResults');
            let output = resultsDiv.textContent + log('检查模块状态...');
            
            try {
                if (window.videoSpeedController) {
                    const controller = window.videoSpeedController;
                    
                    const modules = [
                        'shortcutManager',
                        'mediaDetector', 
                        'playbackController',
                        'uiManager'
                    ];
                    
                    modules.forEach(moduleName => {
                        if (controller[moduleName]) {
                            output += log(`${moduleName}: 已初始化`, 'success');
                            
                            // 检查特定方法
                            if (moduleName === 'shortcutManager') {
                                if (typeof controller[moduleName].registerHandler === 'function') {
                                    output += log(`${moduleName}.registerHandler: 存在`, 'success');
                                } else {
                                    output += log(`${moduleName}.registerHandler: 不存在`, 'error');
                                }
                            }
                        } else {
                            output += log(`${moduleName}: 未初始化`, 'error');
                        }
                    });
                    
                } else {
                    output += log('无法检查模块状态：插件未加载', 'error');
                }
                
            } catch (error) {
                output += log(`模块状态检查失败: ${error.message}`, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function startErrorCapture() {
            if (errorCapturing) return;
            
            errorCapturing = true;
            capturedErrors = [];
            
            const resultsDiv = document.getElementById('errorCapture');
            let output = log('开始错误捕获...');
            resultsDiv.textContent = output;
            
            // 捕获所有错误
            window.addEventListener('error', handleError);
            window.addEventListener('unhandledrejection', handleRejection);
            
            function handleError(event) {
                if (!errorCapturing) return;
                
                const error = {
                    type: 'error',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error,
                    timestamp: new Date().toISOString()
                };
                
                capturedErrors.push(error);
                
                if (error.message.includes('logError')) {
                    output += log(`🎯 logError错误: ${error.message} (${error.filename}:${error.lineno})`, 'error');
                } else {
                    output += log(`其他错误: ${error.message}`, 'warning');
                }
                
                resultsDiv.textContent = output;
            }
            
            function handleRejection(event) {
                if (!errorCapturing) return;
                
                const error = {
                    type: 'rejection',
                    reason: event.reason,
                    timestamp: new Date().toISOString()
                };
                
                capturedErrors.push(error);
                
                const reasonStr = String(event.reason);
                if (reasonStr.includes('logError')) {
                    output += log(`🎯 logError Promise拒绝: ${reasonStr}`, 'error');
                } else {
                    output += log(`Promise拒绝: ${reasonStr}`, 'warning');
                }
                
                resultsDiv.textContent = output;
            }
        }
        
        function stopErrorCapture() {
            errorCapturing = false;
            const resultsDiv = document.getElementById('errorCapture');
            resultsDiv.textContent += log(`错误捕获已停止，共捕获 ${capturedErrors.length} 个错误`);
        }
        
        function clearErrorCapture() {
            capturedErrors = [];
            document.getElementById('errorCapture').textContent = '错误记录已清除';
        }
        
        function triggerShortcutTest() {
            const resultsDiv = document.getElementById('manualResults');
            let output = log('触发快捷键测试...');
            resultsDiv.textContent = output;
            
            try {
                // 模拟按键事件
                const event = new KeyboardEvent('keydown', {
                    key: '=',
                    code: 'Equal',
                    bubbles: true,
                    cancelable: true
                });
                
                document.dispatchEvent(event);
                output += log('快捷键事件已触发', 'success');
                
            } catch (error) {
                output += log(`快捷键测试失败: ${error.message}`, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function triggerModuleInit() {
            const resultsDiv = document.getElementById('manualResults');
            let output = resultsDiv.textContent + log('触发模块初始化...');
            
            try {
                if (window.videoSpeedController) {
                    // 尝试重新初始化
                    if (typeof window.videoSpeedController.init === 'function') {
                        window.videoSpeedController.init();
                        output += log('模块初始化已触发', 'success');
                    } else {
                        output += log('init方法不存在', 'error');
                    }
                } else {
                    output += log('插件不存在，无法触发初始化', 'error');
                }
                
            } catch (error) {
                output += log(`模块初始化失败: ${error.message}`, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function triggerErrorHandling() {
            const resultsDiv = document.getElementById('manualResults');
            let output = resultsDiv.textContent + log('触发错误处理...');
            
            try {
                // 故意触发一个错误
                throw new Error('测试错误');
                
            } catch (error) {
                output += log(`捕获到测试错误: ${error.message}`, 'success');
                
                // 尝试使用插件的错误处理
                if (window.videoSpeedController && window.videoSpeedController.handleError) {
                    try {
                        window.videoSpeedController.handleError('测试上下文', error);
                        output += log('插件错误处理成功', 'success');
                    } catch (handlerError) {
                        output += log(`插件错误处理失败: ${handlerError.message}`, 'error');
                    }
                } else {
                    output += log('插件错误处理方法不存在', 'warning');
                }
            }
            
            resultsDiv.textContent = output;
        }
        
        // 页面加载时自动开始错误捕获
        window.addEventListener('load', () => {
            setTimeout(() => {
                startErrorCapture();
            }, 1000);
        });
    </script>
</body>
</html>
