/**
 * 视频速度控制器主模块
 * 整合所有功能模块，提供统一的接口
 */

import { MEDIA_CONFIG, DEBUG_CONFIG } from './config.js';
import { logInfo, logDebug, logError, checkAPISupport } from './utils.js';
import { handleError, wrapFunction } from './errorHandler.js';
import MediaDetector from './mediaDetector.js';
import ShortcutManager from './shortcutManager.js';
import PlaybackController from './playbackController.js';
import UIManager from './uiManager.js';

/**
 * 视频速度控制器主类
 */
class VideoSpeedController {
    constructor() {
        this.mediaDetector = null;
        this.shortcutManager = null;
        this.playbackController = null;
        this.uiManager = null;
        this.isInitialized = false;
        this.degradedMode = false; // 是否运行在降级模式

        this.init();
    }

    /**
     * 初始化控制器
     */
    async init() {
        const wrapped = wrapFunction(async () => {
            logInfo('视频速度控制器开始初始化');

            // 检查必需的API支持
            const apiCheck = this.checkRequiredAPIs();
            if (!apiCheck.canRun) {
                handleError('浏览器API支持不足', new Error('缺少核心API支持'), {
                    missingAPIs: apiCheck.missingCore
                }, true);
                return;
            }

            // 设置降级模式标志
            // 如果已经预设了降级模式（从main.js），保持该设置
            if (!this.hasOwnProperty('degradedMode')) {
                this.degradedMode = !apiCheck.hasExtension || !apiCheck.isExtensionEnvironment;
            }

            if (this.degradedMode) {
                logInfo('以降级模式运行', {
                    reason: !apiCheck.hasExtension ? '无扩展API支持' : '非扩展环境',
                    isExtensionEnvironment: apiCheck.isExtensionEnvironment
                });
            }

            // 初始化各个模块
            await this.initializeModules();

            // 设置模块间的交互
            this.setupModuleInteractions();

            this.isInitialized = true;
            logInfo('视频速度控制器初始化完成', {
                degradedMode: this.degradedMode
            });
        }, '视频速度控制器初始化失败');

        await wrapped();
    }

    /**
     * 检查必需的API支持
     * @returns {Object} API支持检查结果
     */
    checkRequiredAPIs() {
        const coreAPIs = ['MutationObserver', 'WeakMap'];
        const extensionAPIs = ['chrome.storage'];

        const missingCoreAPIs = coreAPIs.filter(api => !checkAPISupport(api));
        const missingExtensionAPIs = extensionAPIs.filter(api => !checkAPISupport(api));

        // 检查是否在扩展环境中
        const isExtensionEnvironment = typeof chrome !== 'undefined' &&
                                     chrome.runtime &&
                                     typeof chrome.runtime.getURL === 'function';

        const result = {
            hasCore: missingCoreAPIs.length === 0,
            hasExtension: missingExtensionAPIs.length === 0,
            missingCore: missingCoreAPIs,
            missingExtension: missingExtensionAPIs,
            isExtensionEnvironment,
            canRun: missingCoreAPIs.length === 0 // 只要核心API可用就能运行
        };

        if (!result.hasCore) {
            logDebug('缺少核心API支持', { missingCoreAPIs });
        }

        if (!result.hasExtension) {
            logDebug('缺少扩展API支持，将使用降级模式', {
                missingExtensionAPIs,
                isExtensionEnvironment
            });
        }

        // 在非扩展环境下，扩展API不可用是正常的
        if (!isExtensionEnvironment) {
            logInfo('运行在非扩展环境中，将使用降级模式');
        }

        return result;
    }

    /**
     * 检查控制器是否准备就绪
     * @returns {boolean} 是否准备就绪
     */
    isReady() {
        return this.isInitialized && (this.shortcutManager !== null || this.degradedMode);
    }

    /**
     * 初始化各个模块
     */
    async initializeModules() {
        const initResults = {};

        try {
            // 初始化UI管理器
            logDebug('初始化UI管理器...');
            this.uiManager = new UIManager();
            initResults.uiManager = true;

            // 初始化播放控制器
            logDebug('初始化播放控制器...');
            this.playbackController = new PlaybackController();
            initResults.playbackController = true;

            // 初始化媒体检测器
            logDebug('初始化媒体检测器...');
            this.mediaDetector = new MediaDetector();
            initResults.mediaDetector = true;

            // 初始化快捷键管理器（最重要的模块）
            logDebug('初始化快捷键管理器...');
            this.shortcutManager = new ShortcutManager();

            // 确保快捷键管理器正确初始化
            if (this.shortcutManager) {
                await this.shortcutManager.init();

                // 验证关键方法是否存在
                if (typeof this.shortcutManager.registerHandler === 'function') {
                    initResults.shortcutManager = true;
                    logDebug('快捷键管理器初始化完成');
                } else {
                    logError('快捷键管理器缺少registerHandler方法');
                    initResults.shortcutManager = false;
                    this.shortcutManager = null;
                }
            } else {
                logError('快捷键管理器创建失败');
                initResults.shortcutManager = false;
            }

            logDebug('模块初始化结果', initResults);
            logDebug('所有模块初始化完成');

        } catch (error) {
            logError('模块初始化过程中发生错误', error);
            handleError('模块初始化失败', error, { initResults }, !this.degradedMode);

            // 在降级模式下，即使部分模块失败也继续运行
            if (this.degradedMode) {
                logInfo('降级模式：忽略模块初始化错误，继续运行');
            }
        }
    }

    /**
     * 设置模块间的交互
     */
    setupModuleInteractions() {
        try {
            // 检查快捷键管理器是否已初始化
            if (!this.shortcutManager) {
                logError('快捷键管理器未初始化，跳过交互设置');
                return;
            }

            // 检查registerHandler方法是否存在
            if (typeof this.shortcutManager.registerHandler !== 'function') {
                logError('快捷键管理器registerHandler方法不可用');
                return;
            }

            // 注册快捷键处理器
            this.shortcutManager.registerHandler((action, event) => {
                return this.handleShortcutAction(action, event);
            });

            // 添加额外的快捷键支持
            this.setupAdditionalShortcuts();

            logDebug('模块交互设置完成');
        } catch (error) {
            handleError('设置模块交互失败', error, {}, false);
        }
    }

    /**
     * 设置额外的快捷键支持
     */
    setupAdditionalShortcuts() {
        // 监听特殊组合键
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+M 显示媒体列表
            if (event.ctrlKey && event.shiftKey && event.key.toLowerCase() === 'm') {
                event.preventDefault();
                this.showMediaList();
            }

            // Ctrl+Shift+H 显示帮助信息
            if (event.ctrlKey && event.shiftKey && event.key.toLowerCase() === 'h') {
                event.preventDefault();
                this.showHelpInfo();
            }

            // Ctrl+Shift+P 显示性能报告
            if (event.ctrlKey && event.shiftKey && event.key.toLowerCase() === 'p') {
                event.preventDefault();
                this.showPerformanceReport();
            }
        }, true);

        // 设置错误恢复事件监听器
        this.setupErrorRecoveryListeners();
    }

    /**
     * 设置错误恢复事件监听器
     */
    setupErrorRecoveryListeners() {
        // 监听快捷键恢复事件
        document.addEventListener('vsc-recover-shortcuts', () => {
            if (this.shortcutManager) {
                this.shortcutManager.resetToDefaults();
                this.uiManager.showSuccess('快捷键已恢复为默认设置');
            }
        });

        // 监听媒体检测恢复事件
        document.addEventListener('vsc-recover-media-detection', () => {
            if (this.mediaDetector) {
                this.mediaDetector.invalidateCache();
                this.uiManager.showSuccess('媒体检测已重新初始化');
            }
        });

        // 监听缓存恢复事件
        document.addEventListener('vsc-recover-cache', () => {
            if (this.mediaDetector) {
                this.mediaDetector.invalidateCache('manual-recovery');
                this.mediaDetector.resetPerformanceMetrics();
                this.uiManager.showSuccess('缓存已清理');
            }
        });

        // 监听关键错误事件
        document.addEventListener('vsc-critical-error', (event) => {
            const errorInfo = event.detail;
            logInfo('收到关键错误通知', errorInfo);

            // 可以考虑重启扩展
            this.uiManager.showError('发生关键错误，建议刷新页面');
        });
    }

    /**
     * 处理快捷键动作
     * @param {string} action - 动作类型
     * @param {KeyboardEvent} event - 键盘事件
     * @returns {boolean} 是否成功处理
     */
    handleShortcutAction(action, event) {
        if (!this.isInitialized) return false;

        const wrapped = wrapFunction(() => {
            // 特殊处理ESC键退出网页全屏
            if (event.key === 'Escape' && this.uiManager.isLightboxActive()) {
                const lightboxVideo = this.uiManager.getLightboxVideo();
                if (lightboxVideo) {
                    this.uiManager.toggleLightboxFullscreen(lightboxVideo);
                    return true;
                }
            }

            // 在网页全屏模式下特殊处理方向键
            if (this.uiManager.isLightboxActive() && this.handleLightboxArrowKeys(event)) {
                return true;
            }

            // 获取目标媒体元素
            const targetMedia = this.getTargetMedia();
            if (!targetMedia) {
                logDebug('没有找到可控制的媒体元素');
                return false;
            }

            // 高亮显示当前控制的媒体
            this.uiManager.highlightMedia(targetMedia);

            // 处理不同的动作
            return this.executeAction(action, targetMedia);
        }, '处理快捷键动作失败');

        return wrapped() || false;
    }

    /**
     * 处理lightbox模式下的方向键
     * @param {KeyboardEvent} event - 键盘事件
     * @returns {boolean} 是否处理了事件
     */
    handleLightboxArrowKeys(event) {
        const lightboxVideo = this.uiManager.getLightboxVideo();
        if (!lightboxVideo) return false;

        const { key } = event;

        switch (key) {
            case 'ArrowLeft':
                this.playbackController.seekBackward(lightboxVideo);
                this.uiManager.showSeekIndicator('backward', MEDIA_CONFIG.SEEK_STEP, lightboxVideo);
                return true;

            case 'ArrowRight':
                this.playbackController.seekForward(lightboxVideo);
                this.uiManager.showSeekIndicator('forward', MEDIA_CONFIG.SEEK_STEP, lightboxVideo);
                return true;

            case 'ArrowUp':
                this.playbackController.increaseVolume(lightboxVideo);
                this.uiManager.showVolumeIndicator(lightboxVideo.volume, lightboxVideo);
                return true;

            case 'ArrowDown':
                this.playbackController.decreaseVolume(lightboxVideo);
                this.uiManager.showVolumeIndicator(lightboxVideo.volume, lightboxVideo);
                return true;

            default:
                return false;
        }
    }

    /**
     * 执行具体的动作
     * @param {string} action - 动作类型
     * @param {HTMLMediaElement} media - 媒体元素
     * @returns {boolean} 是否成功执行
     */
    executeAction(action, media) {
        switch (action) {
            case 'increase':
                if (this.playbackController.increaseSpeed(media)) {
                    this.uiManager.showSpeedIndicator(media.playbackRate, media);
                    return true;
                }
                break;

            case 'decrease':
                if (this.playbackController.decreaseSpeed(media)) {
                    this.uiManager.showSpeedIndicator(media.playbackRate, media);
                    return true;
                }
                break;

            case 'reset':
                if (this.playbackController.resetSpeed(media)) {
                    this.uiManager.showSpeedIndicator(media.playbackRate, media);
                    return true;
                }
                break;



            case 'toggle-fullscreen':
                if (media.tagName === 'VIDEO') {
                    return this.uiManager.toggleLightboxFullscreen(media);
                }
                break;

            default:
                logDebug('未知的动作类型', { action });
                return false;
        }

        return false;
    }

    /**
     * 获取目标媒体元素
     * @returns {HTMLMediaElement|null} 目标媒体元素
     */
    getTargetMedia() {
        // 如果在lightbox模式下，优先返回lightbox中的视频
        if (this.uiManager.isLightboxActive()) {
            const lightboxVideo = this.uiManager.getLightboxVideo();
            if (lightboxVideo) return lightboxVideo;
        }

        // 否则使用媒体检测器获取目标媒体
        return this.mediaDetector.getTargetMedia();
    }

    /**
     * 获取当前媒体信息
     * @returns {Object|null} 媒体信息
     */
    getCurrentMediaInfo() {
        const media = this.getTargetMedia();
        return media ? this.playbackController.getMediaInfo(media) : null;
    }

    /**
     * 获取快捷键配置
     * @returns {Object} 快捷键配置
     */
    getShortcuts() {
        return this.shortcutManager ? this.shortcutManager.getShortcuts() : {};
    }

    /**
     * 保存快捷键配置
     * @param {Object} shortcuts - 快捷键配置
     * @returns {Promise<boolean>} 是否保存成功
     */
    async saveShortcuts(shortcuts) {
        if (!this.shortcutManager) return false;
        return await this.shortcutManager.saveShortcutSettings(shortcuts);
    }

    /**
     * 重置快捷键为默认值
     * @returns {Promise<boolean>} 是否重置成功
     */
    async resetShortcuts() {
        if (!this.shortcutManager) return false;
        await this.shortcutManager.resetToDefaults();
        return true;
    }

    /**
     * 获取错误统计信息
     * @returns {Object} 错误统计
     */
    getErrorStats() {
        return this.errorHandler ? this.errorHandler.getErrorStats() : null;
    }

    /**
     * 显示媒体列表
     */
    showMediaList() {
        if (!this.isInitialized) return;

        const wrapped = wrapFunction(() => {
            const allMedia = this.mediaDetector.getAllMediaElements();
            const currentMedia = this.getTargetMedia();

            this.uiManager.showMediaList(allMedia, currentMedia);
        }, '显示媒体列表失败');

        wrapped();
    }

    /**
     * 显示帮助信息
     */
    showHelpInfo() {
        if (!this.isInitialized) return;

        const shortcuts = this.getShortcuts();
        const helpText = `
快捷键帮助:
• ${shortcuts.increase || '='}: 加速播放
• ${shortcuts.decrease || '-'}: 减速播放
• ${shortcuts.reset || '0'}: 重置速度
• ${shortcuts['toggle-fullscreen'] || 'f'}: 网页全屏
• 空格键: 播放/暂停 (原生控制)
• Ctrl+Shift+M: 显示媒体列表
• Ctrl+Shift+H: 显示此帮助
• Ctrl+Shift+P: 显示性能报告
        `.trim();

        this.uiManager.showIndicator(helpText);
    }

    /**
     * 显示性能报告
     */
    showPerformanceReport() {
        if (!this.isInitialized || !this.mediaDetector) return;

        const wrapped = wrapFunction(() => {
            const stats = this.mediaDetector.getCacheStats();
            const metrics = stats.performanceMetrics;

            const totalQueries = metrics.cacheHits + metrics.cacheMisses;
            const hitRate = totalQueries > 0 ?
                (metrics.cacheHits / totalQueries * 100).toFixed(1) : '0';

            const reportText = `
性能报告:
• 缓存命中率: ${hitRate}%
• 平均查询时间: ${metrics.averageQueryTime.toFixed(1)}ms
• 缓存版本: ${stats.version}
• 媒体元素: ${stats.elementCount}个
• 缓存年龄: ${(stats.age / 1000).toFixed(1)}s
• Shadow DOM查询: ${metrics.shadowDOMQueries}次
            `.trim();

            this.uiManager.showIndicator(reportText);

            // 同时在控制台输出详细信息（安全检查）
            try {
                if (DEBUG_CONFIG && DEBUG_CONFIG.ENABLED) {
                    console.group('📊 详细性能报告');
                    console.log('缓存统计:', stats);
                    console.log('性能指标:', metrics);
                    console.groupEnd();
                }
            } catch (error) {
                console.warn('[VideoSpeedController] DEBUG_CONFIG访问失败，跳过详细报告', error);
            }
        }, '显示性能报告失败');

        wrapped();
    }

    /**
     * 检查是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * 清理资源
     */
    destroy() {
        logInfo('开始清理视频速度控制器资源');

        if (this.mediaDetector) {
            this.mediaDetector.destroy();
        }

        if (this.shortcutManager) {
            this.shortcutManager.destroy();
        }

        if (this.playbackController) {
            this.playbackController.destroy();
        }

        if (this.uiManager) {
            this.uiManager.destroy();
        }

        this.isInitialized = false;
        logInfo('视频速度控制器资源清理完成');
    }
}

export default VideoSpeedController;
