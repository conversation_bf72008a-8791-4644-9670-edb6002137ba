<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-results {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        .youtube-link {
            display: inline-block;
            margin: 10px 0;
            padding: 10px 15px;
            background: #ff0000;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #4caf50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
    </style>
</head>
<body>
    <h1>🎉 最终修复验证</h1>
    
    <div class="success-box">
        <h3>✅ 已修复的问题</h3>
        <ol>
            <li><strong>TrustedHTML错误</strong> - 使用安全的DOM操作替代innerHTML</li>
            <li><strong>registerHandler null错误</strong> - 增强模块初始化和null检查</li>
            <li><strong>logError未定义错误</strong> - 修复导入问题和循环依赖</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🔍 综合功能测试</h3>
        <p>测试所有修复后的功能是否正常工作</p>
        
        <button onclick="runComprehensiveTest()">运行综合测试</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div class="test-results" id="comprehensiveResults">
            点击"运行综合测试"开始...
        </div>
    </div>

    <div class="test-section">
        <h3>🎬 YouTube实际验证</h3>
        <p>在YouTube上验证修复效果</p>
        
        <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" target="_blank" class="youtube-link">
            🎵 测试YouTube视频
        </a>
        
        <div style="margin: 10px 0;">
            <strong>验证清单：</strong>
            <ul>
                <li>✅ 页面加载时无TrustedHTML错误</li>
                <li>✅ 页面加载时无registerHandler错误</li>
                <li>✅ 页面加载时无logError未定义错误</li>
                <li>✅ 插件正常加载和初始化</li>
                <li>✅ 速度控制功能正常（=、-、0键）</li>
                <li>✅ 原生播放控制正常（空格键等）</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3>📊 实时错误监控</h3>
        <p>监控页面错误，确保没有遗漏的问题</p>
        
        <button onclick="startFinalMonitoring()">开始最终监控</button>
        <button onclick="stopFinalMonitoring()">停止监控</button>
        
        <div class="test-results" id="finalMonitoring">
            最终监控未启动...
        </div>
    </div>

    <script>
        let finalMonitoring = false;
        let errorCount = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            return `[${timestamp}] ${prefix} ${message}\n`;
        }
        
        function clearResults() {
            document.getElementById('comprehensiveResults').textContent = '测试结果已清除';
        }
        
        async function runComprehensiveTest() {
            const resultsDiv = document.getElementById('comprehensiveResults');
            let output = log('开始综合功能测试...');
            resultsDiv.textContent = output;
            
            // 测试1: 基础API检查
            output += log('=== 基础API检查 ===');
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    output += log('Chrome扩展API: 可用', 'success');
                } else {
                    output += log('Chrome扩展API: 不可用', 'warning');
                }
                
                const webAPIs = ['MutationObserver', 'WeakMap', 'IntersectionObserver'];
                webAPIs.forEach(api => {
                    if (typeof window[api] !== 'undefined') {
                        output += log(`${api}: 可用`, 'success');
                    } else {
                        output += log(`${api}: 不可用`, 'error');
                    }
                });
            } catch (error) {
                output += log(`API检查失败: ${error.message}`, 'error');
            }
            resultsDiv.textContent = output;
            
            // 测试2: 插件状态检查
            output += log('\n=== 插件状态检查 ===');
            try {
                if (window.videoSpeedController) {
                    output += log('插件实例: 存在', 'success');
                    
                    if (window.videoSpeedController.isReady && window.videoSpeedController.isReady()) {
                        output += log('插件状态: 已就绪', 'success');
                    } else {
                        output += log('插件状态: 未就绪', 'warning');
                    }
                    
                    if (window.videoSpeedController.degradedMode) {
                        output += log('运行模式: 降级模式', 'warning');
                    } else {
                        output += log('运行模式: 正常模式', 'success');
                    }
                } else {
                    output += log('插件实例: 不存在', 'error');
                }
            } catch (error) {
                output += log(`插件状态检查失败: ${error.message}`, 'error');
            }
            resultsDiv.textContent = output;
            
            // 测试3: 模块完整性检查
            output += log('\n=== 模块完整性检查 ===');
            try {
                if (window.videoSpeedController) {
                    const modules = ['shortcutManager', 'mediaDetector', 'playbackController', 'uiManager'];
                    modules.forEach(moduleName => {
                        if (window.videoSpeedController[moduleName]) {
                            output += log(`${moduleName}: 已初始化`, 'success');
                        } else {
                            output += log(`${moduleName}: 未初始化`, 'error');
                        }
                    });
                    
                    // 特别检查registerHandler
                    if (window.videoSpeedController.shortcutManager && 
                        typeof window.videoSpeedController.shortcutManager.registerHandler === 'function') {
                        output += log('registerHandler方法: 可用', 'success');
                    } else {
                        output += log('registerHandler方法: 不可用', 'error');
                    }
                }
            } catch (error) {
                output += log(`模块检查失败: ${error.message}`, 'error');
            }
            resultsDiv.textContent = output;
            
            // 测试4: DOM操作安全性测试
            output += log('\n=== DOM操作安全性测试 ===');
            try {
                // 测试安全的DOM创建
                const testDiv = document.createElement('div');
                testDiv.textContent = '安全文本内容';
                testDiv.style.cssText = 'display: none;';
                document.body.appendChild(testDiv);
                document.body.removeChild(testDiv);
                output += log('安全DOM操作: 成功', 'success');
                
                // 测试innerHTML（应该在严格CSP下失败）
                try {
                    const unsafeDiv = document.createElement('div');
                    unsafeDiv.innerHTML = '<span>测试HTML</span>';
                    output += log('innerHTML操作: 成功（非严格CSP环境）', 'warning');
                } catch (htmlError) {
                    if (htmlError.message.includes('TrustedHTML')) {
                        output += log('innerHTML操作: 被CSP阻止（正常）', 'success');
                    } else {
                        output += log(`innerHTML操作: 其他错误 - ${htmlError.message}`, 'error');
                    }
                }
            } catch (error) {
                output += log(`DOM操作测试失败: ${error.message}`, 'error');
            }
            resultsDiv.textContent = output;
            
            // 测试5: 快捷键功能测试
            output += log('\n=== 快捷键功能测试 ===');
            try {
                // 模拟快捷键事件
                const testKeys = ['=', '-', '0', 'f'];
                testKeys.forEach(key => {
                    try {
                        const event = new KeyboardEvent('keydown', {
                            key: key,
                            bubbles: true,
                            cancelable: true
                        });
                        document.dispatchEvent(event);
                        output += log(`快捷键 ${key}: 事件已触发`, 'success');
                    } catch (keyError) {
                        output += log(`快捷键 ${key}: 触发失败 - ${keyError.message}`, 'error');
                    }
                });
            } catch (error) {
                output += log(`快捷键测试失败: ${error.message}`, 'error');
            }
            resultsDiv.textContent = output;
            
            output += log('\n=== 综合测试完成 ===');
            output += log('如果没有错误信息，说明修复成功！', 'success');
            resultsDiv.textContent = output;
        }
        
        function startFinalMonitoring() {
            if (finalMonitoring) return;
            
            finalMonitoring = true;
            errorCount = 0;
            
            const resultsDiv = document.getElementById('finalMonitoring');
            let output = log('开始最终错误监控...');
            resultsDiv.textContent = output;
            
            // 监听所有错误
            window.addEventListener('error', handleError);
            window.addEventListener('unhandledrejection', handleRejection);
            
            function handleError(event) {
                if (!finalMonitoring) return;
                errorCount++;
                
                const error = event.error || event;
                const message = error.message || event.message || '未知错误';
                
                if (message.includes('TrustedHTML')) {
                    output += log(`❌ TrustedHTML错误 #${errorCount}: ${message}`, 'error');
                } else if (message.includes('registerHandler')) {
                    output += log(`❌ registerHandler错误 #${errorCount}: ${message}`, 'error');
                } else if (message.includes('logError')) {
                    output += log(`❌ logError错误 #${errorCount}: ${message}`, 'error');
                } else {
                    output += log(`⚠️ 其他错误 #${errorCount}: ${message}`, 'warning');
                }
                
                resultsDiv.textContent = output;
            }
            
            function handleRejection(event) {
                if (!finalMonitoring) return;
                errorCount++;
                
                const reason = String(event.reason || '未知Promise拒绝');
                
                if (reason.includes('TrustedHTML') || reason.includes('registerHandler') || reason.includes('logError')) {
                    output += log(`❌ 关键Promise拒绝 #${errorCount}: ${reason}`, 'error');
                } else {
                    output += log(`⚠️ Promise拒绝 #${errorCount}: ${reason}`, 'warning');
                }
                
                resultsDiv.textContent = output;
            }
            
            // 5秒后显示初步结果
            setTimeout(() => {
                if (errorCount === 0) {
                    output += log('🎉 5秒内无关键错误，修复效果良好！', 'success');
                } else {
                    output += log(`⚠️ 5秒内检测到 ${errorCount} 个错误`, 'warning');
                }
                resultsDiv.textContent = output;
            }, 5000);
        }
        
        function stopFinalMonitoring() {
            finalMonitoring = false;
            const resultsDiv = document.getElementById('finalMonitoring');
            resultsDiv.textContent += log(`最终监控已停止，共检测到 ${errorCount} 个错误`);
        }
        
        // 页面加载时自动开始监控
        window.addEventListener('load', () => {
            setTimeout(() => {
                startFinalMonitoring();
                // 3秒后自动运行综合测试
                setTimeout(() => {
                    runComprehensiveTest();
                }, 3000);
            }, 1000);
        });
    </script>
</body>
</html>
