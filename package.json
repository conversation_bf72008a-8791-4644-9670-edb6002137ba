{"name": "chrome-plugin-video-manager", "version": "2.0.0", "description": "Chrome扩展视频速度控制器 - 重构版本", "main": "src/main.js", "type": "module", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ tests/ --ext .js", "lint:fix": "eslint src/ tests/ --ext .js --fix", "format": "prettier --write \"src/**/*.js\" \"tests/**/*.js\" \"*.js\"", "format:check": "prettier --check \"src/**/*.js\" \"tests/**/*.js\" \"*.js\"", "build": "npm run lint && npm run test", "build:prod": "npm run lint && npm run test:coverage && npm run format:check", "dev": "npm run lint:fix && npm run test:watch", "clean": "rm -rf coverage/ node_modules/.cache/", "validate": "npm run lint && npm run test && npm run format:check", "precommit": "npm run validate"}, "keywords": ["chrome-extension", "video-speed-control", "media-control", "browser-extension", "javascript", "es6"], "author": "Video Speed Controller Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.0", "@babel/plugin-proposal-class-properties": "^7.18.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.22.0", "babel-jest": "^29.5.0", "core-js": "^3.30.0", "eslint": "^8.42.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "^2.8.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["Chrome >= 88"], "eslintConfig": {"extends": ["./.eslintrc.js"]}, "prettier": {"semi": true, "trailingComma": "none", "singleQuote": true, "printWidth": 100, "tabWidth": 4, "useTabs": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "https://github.com/your-username/chrome-plugin-video-manager.git"}, "bugs": {"url": "https://github.com/your-username/chrome-plugin-video-manager/issues"}, "homepage": "https://github.com/your-username/chrome-plugin-video-manager#readme"}