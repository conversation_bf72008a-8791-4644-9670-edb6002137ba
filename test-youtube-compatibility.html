<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube兼容性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        
        .test-results {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success {
            color: #4caf50;
            font-weight: bold;
        }
        
        .error {
            color: #f44336;
            font-weight: bold;
        }
        
        .warning {
            color: #ff9800;
            font-weight: bold;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #4caf50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        .status-unknown { background-color: #ccc; }
        
        .youtube-link {
            display: inline-block;
            margin: 10px 0;
            padding: 10px 15px;
            background: #ff0000;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .youtube-link:hover {
            background: #cc0000;
        }
    </style>
</head>
<body>
    <h1>🎬 YouTube兼容性测试</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>这个页面用于测试修复后的Chrome视频管理插件在YouTube上的兼容性。</p>
        <ol>
            <li>首先在此页面运行基础测试</li>
            <li>然后访问YouTube进行实际测试</li>
            <li>验证所有功能是否正常工作</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🔧 基础API测试</h3>
        <button onclick="runBasicTests()">运行基础测试</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div class="test-results" id="basicResults">
            点击"运行基础测试"开始...
        </div>
    </div>

    <div class="test-section">
        <h3>🎥 YouTube测试链接</h3>
        <p>在基础测试通过后，点击下面的链接访问YouTube进行实际测试：</p>
        
        <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" target="_blank" class="youtube-link">
            🎵 测试视频 1 (音乐)
        </a>
        
        <a href="https://www.youtube.com/watch?v=jNQXAC9IVRw" target="_blank" class="youtube-link">
            🎬 测试视频 2 (短片)
        </a>
        
        <div class="instructions">
            <h4>在YouTube上测试以下功能：</h4>
            <ul>
                <li><strong>插件加载</strong>：检查是否有错误消息</li>
                <li><strong>速度控制</strong>：= 加速，- 减速，0 重置</li>
                <li><strong>原生控制</strong>：空格键播放/暂停</li>
                <li><strong>网页全屏</strong>：F键进入/退出全屏</li>
                <li><strong>方向键控制</strong>：← → 快退/快进</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3>📊 实时状态监控</h3>
        <button onclick="startMonitoring()">开始监控</button>
        <button onclick="stopMonitoring()">停止监控</button>
        
        <div class="test-results" id="monitorResults">
            点击"开始监控"开始实时监控...
        </div>
    </div>

    <script>
        let monitoringInterval = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✓' : type === 'error' ? '✗' : type === 'warning' ? '⚠' : 'ℹ';
            return `[${timestamp}] ${prefix} ${message}\n`;
        }
        
        function clearResults() {
            document.getElementById('basicResults').textContent = '测试结果已清除';
            document.getElementById('monitorResults').textContent = '监控已清除';
        }
        
        async function runBasicTests() {
            const resultsDiv = document.getElementById('basicResults');
            resultsDiv.textContent = '';
            
            let output = log('开始基础API测试...');
            resultsDiv.textContent = output;
            
            // 测试1：检查Chrome扩展API
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    output += log('Chrome扩展API可用', 'success');
                } else {
                    output += log('Chrome扩展API不可用', 'error');
                }
            } catch (error) {
                output += log('Chrome扩展API检查失败: ' + error.message, 'error');
            }
            resultsDiv.textContent = output;
            
            // 测试2：检查存储API
            try {
                if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                    output += log('Chrome存储API可用', 'success');
                    
                    // 尝试读取存储
                    chrome.storage.sync.get(['test'], (result) => {
                        if (chrome.runtime.lastError) {
                            output += log('存储读取失败: ' + chrome.runtime.lastError.message, 'error');
                        } else {
                            output += log('存储读取成功', 'success');
                        }
                        resultsDiv.textContent = output;
                    });
                } else {
                    output += log('Chrome存储API不可用', 'warning');
                }
            } catch (error) {
                output += log('Chrome存储API检查失败: ' + error.message, 'error');
            }
            resultsDiv.textContent = output;
            
            // 测试3：检查核心Web API
            const webAPIs = [
                'MutationObserver',
                'IntersectionObserver', 
                'WeakMap',
                'requestIdleCallback',
                'ResizeObserver'
            ];
            
            webAPIs.forEach(api => {
                try {
                    if (typeof window[api] !== 'undefined') {
                        output += log(`${api} 可用`, 'success');
                    } else {
                        output += log(`${api} 不可用`, 'warning');
                    }
                } catch (error) {
                    output += log(`${api} 检查失败: ${error.message}`, 'error');
                }
            });
            resultsDiv.textContent = output;
            
            // 测试4：检查插件是否已加载
            setTimeout(() => {
                if (window.videoSpeedController) {
                    output += log('视频速度控制器已加载', 'success');
                    
                    if (window.videoSpeedController.isReady && window.videoSpeedController.isReady()) {
                        output += log('视频速度控制器已就绪', 'success');
                    } else {
                        output += log('视频速度控制器未就绪', 'warning');
                    }
                    
                    if (window.videoSpeedController.degradedMode) {
                        output += log('运行在降级模式', 'warning');
                    } else {
                        output += log('运行在正常模式', 'success');
                    }
                } else {
                    output += log('视频速度控制器未加载', 'error');
                }
                resultsDiv.textContent = output;
                
                output += log('基础测试完成');
                resultsDiv.textContent = output;
            }, 2000);
        }
        
        function startMonitoring() {
            if (monitoringInterval) {
                stopMonitoring();
            }
            
            const resultsDiv = document.getElementById('monitorResults');
            let output = log('开始实时监控...');
            resultsDiv.textContent = output;
            
            monitoringInterval = setInterval(() => {
                const status = getSystemStatus();
                output += log(`状态: ${status.summary}`, status.type);
                
                // 保持最近50行
                const lines = output.split('\n');
                if (lines.length > 50) {
                    output = lines.slice(-50).join('\n');
                }
                
                resultsDiv.textContent = output;
            }, 3000);
        }
        
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                
                const resultsDiv = document.getElementById('monitorResults');
                resultsDiv.textContent += log('监控已停止');
            }
        }
        
        function getSystemStatus() {
            try {
                // 检查插件状态
                if (!window.videoSpeedController) {
                    return { summary: '插件未加载', type: 'error' };
                }
                
                if (!window.videoSpeedController.isReady()) {
                    return { summary: '插件未就绪', type: 'warning' };
                }
                
                if (window.videoSpeedController.degradedMode) {
                    return { summary: '降级模式运行', type: 'warning' };
                }
                
                // 检查媒体元素
                const videos = document.querySelectorAll('video');
                if (videos.length > 0) {
                    return { summary: `正常运行 (${videos.length}个视频)`, type: 'success' };
                }
                
                return { summary: '正常运行 (无视频)', type: 'success' };
                
            } catch (error) {
                return { summary: '状态检查失败: ' + error.message, type: 'error' };
            }
        }
        
        // 监听插件加载事件
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.videoSpeedController) {
                    console.log('视频速度控制器已自动加载');
                }
            }, 1000);
        });
        
        // 监听错误事件
        window.addEventListener('error', (event) => {
            console.error('页面错误:', event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
        });
    </script>
</body>
</html>
