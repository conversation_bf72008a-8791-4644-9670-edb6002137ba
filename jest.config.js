export default {
    // 测试环境
    testEnvironment: 'jsdom',
    
    // 测试文件匹配模式
    testMatch: [
        '**/tests/**/*.test.js',
        '**/src/**/*.test.js',
        '**/__tests__/**/*.js'
    ],
    
    // 覆盖率收集
    collectCoverage: true,
    collectCoverageFrom: [
        'src/**/*.js',
        '!src/**/*.test.js',
        '!src/content.js', // 排除入口文件
        '!**/node_modules/**'
    ],
    
    // 覆盖率报告
    coverageDirectory: 'coverage',
    coverageReporters: [
        'text',
        'lcov',
        'html'
    ],
    
    // 覆盖率阈值
    coverageThreshold: {
        global: {
            branches: 70,
            functions: 70,
            lines: 70,
            statements: 70
        }
    },
    
    // 模块路径映射
    moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/src/$1'
    },
    
    // 设置文件
    setupFilesAfterEnv: [
        '<rootDir>/tests/setup.js'
    ],
    
    // 模拟模块
    moduleFileExtensions: ['js', 'json'],
    
    // 转换配置
    transform: {
        '^.+\\.js$': 'babel-jest'
    },
    
    // 忽略转换的模块
    transformIgnorePatterns: [
        'node_modules/(?!(chrome-extension-async)/)'
    ],
    
    // 全局变量
    globals: {
        chrome: {},
        browser: {}
    },
    
    // 测试超时
    testTimeout: 10000,
    
    // 详细输出
    verbose: true,
    
    // 清理模拟
    clearMocks: true,
    restoreMocks: true,
    
    // 错误处理
    errorOnDeprecated: true
};
