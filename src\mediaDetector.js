/**
 * 媒体检测模块
 * 负责检测、缓存和选择页面上的视频/音频元素
 */

import {
    PERFORMANCE_CONFIG,
    MEDIA_SELECTION_PRIORITY,
    UI_CONFIG,
    DEBUG_CONFIG
} from './config.js';
import {
    debounce,
    isElementInViewport,
    isInIframe,
    getElementSize,
    logDebug,
    logWarn,
    checkAPISupport
} from './utils.js';
import { handleError, wrapFunction } from './errorHandler.js';

/**
 * 媒体检测器类
 */
class MediaDetector {
    constructor() {
        this.mediaElementsCache = {
            timestamp: 0,
            elements: [],
            shadowElements: [],
            isStale: true,
            timeoutId: null,
            version: 0,
            lastMutationTime: 0
        };

        this.elementVisibilityMap = new WeakMap();
        this.mediaSizeCache = new WeakMap();
        this.lastActiveMedia = null;
        this.intersectionObserver = null;
        this.mutationObserver = null;
        this.mediaCheckInterval = null;
        this.isFullFunctionalityInitialized = false;

        // 性能监控
        this.performanceMetrics = {
            cacheHits: 0,
            cacheMisses: 0,
            shadowDOMQueries: 0,
            averageQueryTime: 0
        };

        // 智能缓存配置
        this.cacheConfig = {
            maxAge: PERFORMANCE_CONFIG.CACHE_EXPIRE_TIME,
            maxElements: 100,
            adaptiveExpiry: true
        };

        this.init();
    }

    /**
     * 初始化媒体检测器
     */
    init() {
        this.setupIntersectionObserver();
        this.setupMutationObserver();
        this.setupMediaElementDetection();
        this.setupEventDelegation();

        // 启动性能监控（安全检查）
        try {
            if (DEBUG_CONFIG && DEBUG_CONFIG.PERFORMANCE_MONITORING) {
                this.startPerformanceMonitoring();
            }
        } catch (error) {
            console.warn('[MediaDetector] DEBUG_CONFIG访问失败，跳过性能监控', error);
        }

        // 定期优化性能
        setTimeout(() => {
            this.optimizePerformance();
        }, 30000); // 30秒后开始优化
    }

    /**
     * 设置IntersectionObserver
     */
    setupIntersectionObserver() {
        if (!checkAPISupport('IntersectionObserver')) {
            logWarn('IntersectionObserver不支持，将使用备用方法');
            return;
        }

        try {
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    this.elementVisibilityMap.set(entry.target, entry.isIntersecting);
                });
            });
        } catch (error) {
            handleError('设置IntersectionObserver失败', error);
        }
    }

    /**
     * 设置MutationObserver
     */
    setupMutationObserver() {
        if (!checkAPISupport('MutationObserver')) {
            logWarn('MutationObserver不支持，将使用备用方法');
            return;
        }

        try {
            this.mutationObserver = new MutationObserver(debounce((mutations) => {
                // 记录DOM变化时间
                this.mediaElementsCache.lastMutationTime = Date.now();

                // 检查是否有媒体相关的变化
                const hasMediaChanges = this.hasMediaRelatedMutations(mutations);

                if (hasMediaChanges) {
                    logDebug('检测到媒体相关DOM变化', {
                        mutationCount: mutations.length
                    });
                    this.invalidateCache();
                    this.checkForMediaElements();
                } else {
                    // 即使没有媒体变化，也要标记缓存可能过期
                    this.mediaElementsCache.isStale = true;
                }
            }, PERFORMANCE_CONFIG.DEBOUNCE_DELAY));

            this.mutationObserver.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['src', 'controls', 'autoplay', 'muted'],
                characterData: false
            });
        } catch (error) {
            handleError('设置MutationObserver失败', error);
        }
    }

    /**
     * 检查变化是否与媒体相关
     * @param {Array} mutations - MutationRecord数组
     * @returns {boolean} 是否有媒体相关变化
     */
    hasMediaRelatedMutations(mutations) {
        for (const mutation of mutations) {
            // 检查添加的节点
            if (mutation.type === 'childList') {
                for (const node of mutation.addedNodes) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (this.isMediaElement(node) ||
                            node.querySelector && node.querySelector('video, audio')) {
                            return true;
                        }
                    }
                }

                // 检查移除的节点
                for (const node of mutation.removedNodes) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        if (this.isMediaElement(node) ||
                            node.querySelector && node.querySelector('video, audio')) {
                            return true;
                        }
                    }
                }
            }

            // 检查属性变化
            if (mutation.type === 'attributes') {
                const target = mutation.target;
                if (this.isMediaElement(target) ||
                    target.closest && target.closest('video, audio')) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查元素是否为媒体元素
     * @param {Element} element - 要检查的元素
     * @returns {boolean} 是否为媒体元素
     */
    isMediaElement(element) {
        return element.tagName === 'VIDEO' || element.tagName === 'AUDIO';
    }

    /**
     * 设置媒体元素检测
     */
    setupMediaElementDetection() {
        // 初始检查
        this.checkForMediaElements();

        // 设置递减间隔检查
        this.scheduleNextCheck();

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.invalidateCache();
                this.checkForMediaElements();
            }
        });
    }

    /**
     * 设置事件委托
     */
    setupEventDelegation() {
        const handleMouseEvent = wrapFunction((event) => {
            const target = event.target;
            if (target.tagName === 'VIDEO' || target.tagName === 'AUDIO') {
                this.lastActiveMedia = target;
                logDebug('媒体元素交互', { tagName: target.tagName, src: target.src });
            }
        }, '媒体事件处理');

        // 使用事件委托监听媒体元素的鼠标事件
        document.addEventListener('mouseenter', handleMouseEvent, true);
        document.addEventListener('click', handleMouseEvent, true);
        document.addEventListener('play', handleMouseEvent, true);
    }

    /**
     * 检查页面媒体元素
     */
    checkForMediaElements() {
        const wrapped = wrapFunction(() => {
            const hasMedia = document.querySelector('video, audio') !== null;
            if (hasMedia) {
                // 找到媒体元素，停止定时检查
                if (this.mediaCheckInterval) {
                    clearInterval(this.mediaCheckInterval);
                    this.mediaCheckInterval = null;
                }

                this.invalidateCache();
                this.handleShadowDOMMedia();
            }
        }, '检查媒体元素');

        wrapped();
    }

    /**
     * 安排下次检查
     */
    scheduleNextCheck() {
        if (this.mediaCheckInterval) {
            clearInterval(this.mediaCheckInterval);
        }

        let checkCount = 0;
        const intervals = PERFORMANCE_CONFIG.MEDIA_CHECK_INTERVALS;

        const scheduleCheck = () => {
            if (checkCount < intervals.length) {
                const interval = intervals[checkCount];
                this.mediaCheckInterval = setTimeout(() => {
                    this.checkForMediaElements();
                    checkCount++;
                    scheduleCheck();
                }, interval);
            }
        };

        scheduleCheck();
    }

    /**
     * 处理Shadow DOM中的媒体元素
     */
    handleShadowDOMMedia() {
        const wrapped = wrapFunction(() => {
            const mediaInShadow = this.querySelectorAllIncludingShadowDOM('video, audio');
            if (mediaInShadow.length > 0) {
                logDebug('发现Shadow DOM媒体元素', { count: mediaInShadow.length });

                // 观察这些元素的可见性
                if (this.intersectionObserver) {
                    mediaInShadow.forEach(media => {
                        this.intersectionObserver.observe(media);
                    });
                }
            }
        }, 'Shadow DOM媒体处理');

        wrapped();
    }

    /**
     * 查询包括Shadow DOM在内的所有元素（优化版本）
     * @param {string} selector - CSS选择器
     * @returns {Array} 匹配的元素数组
     */
    querySelectorAllIncludingShadowDOM(selector) {
        try {
            // 检查缓存
            if (!this.mediaElementsCache.isStale && this.mediaElementsCache.shadowElements.length > 0) {
                return this.mediaElementsCache.shadowElements;
            }

            const elements = Array.from(document.querySelectorAll(selector));

            // 使用更高效的Shadow DOM查询策略
            const shadowElements = this.queryShadowDOMOptimized(selector);
            elements.push(...shadowElements);

            // 缓存结果
            this.mediaElementsCache.shadowElements = elements;
            return elements;
        } catch (error) {
            handleError('Shadow DOM查询失败', error);
            return [];
        }
    }

    /**
     * 优化的Shadow DOM查询方法
     * @param {string} selector - CSS选择器
     * @returns {Array} Shadow DOM中的匹配元素
     */
    queryShadowDOMOptimized(selector) {
        const shadowElements = [];
        const processedShadowRoots = new WeakSet();

        try {
            // 使用TreeWalker进行更高效的DOM遍历
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_ELEMENT,
                {
                    acceptNode: (node) => {
                        // 只处理可能包含Shadow DOM的元素
                        return this.isLikelyShadowHost(node) ?
                            NodeFilter.FILTER_ACCEPT :
                            NodeFilter.FILTER_SKIP;
                    }
                }
            );

            const shadowHosts = [];
            let node;

            // 收集所有可能的Shadow Host
            while (node = walker.nextNode()) {
                if (node.shadowRoot && !processedShadowRoots.has(node.shadowRoot)) {
                    shadowHosts.push(node);
                    processedShadowRoots.add(node.shadowRoot);
                }

                // 限制处理数量以避免性能问题
                if (shadowHosts.length >= PERFORMANCE_CONFIG.SHADOW_DOM_QUERY_LIMIT) {
                    break;
                }
            }

            // 在收集到的Shadow DOM中查询媒体元素
            for (const host of shadowHosts) {
                try {
                    const mediaInShadow = host.shadowRoot.querySelectorAll(selector);
                    shadowElements.push(...Array.from(mediaInShadow));
                } catch (shadowError) {
                    // 某些Shadow DOM可能无法访问，忽略错误
                    logDebug('无法访问Shadow DOM', { host: host.tagName });
                }
            }

            logDebug('Shadow DOM查询完成', {
                hostsFound: shadowHosts.length,
                elementsFound: shadowElements.length
            });

        } catch (error) {
            handleError('优化Shadow DOM查询失败', error);
        }

        return shadowElements;
    }

    /**
     * 判断元素是否可能是Shadow Host
     * @param {Element} element - 要检查的元素
     * @returns {boolean} 是否可能是Shadow Host
     */
    isLikelyShadowHost(element) {
        // 常见的可能包含Shadow DOM的元素
        const likelyHosts = [
            'video', 'audio', 'iframe', 'embed', 'object',
            'custom-element', 'web-component'
        ];

        const tagName = element.tagName.toLowerCase();

        // 检查标签名
        if (likelyHosts.includes(tagName)) {
            return true;
        }

        // 检查自定义元素（包含连字符）
        if (tagName.includes('-')) {
            return true;
        }

        // 检查是否已经有Shadow Root
        if (element.shadowRoot) {
            return true;
        }

        // 检查特定的类名或属性
        if (element.classList.contains('shadow-host') ||
            element.hasAttribute('shadow-root')) {
            return true;
        }

        return false;
    }

    /**
     * 获取所有媒体元素（改进的缓存版本）
     * @returns {Array} 媒体元素数组
     */
    getAllMediaElements() {
        const startTime = performance.now();

        // 智能缓存检查
        if (this.isCacheValid()) {
            this.performanceMetrics.cacheHits++;
            logDebug('缓存命中', {
                elements: this.mediaElementsCache.elements.length,
                age: Date.now() - this.mediaElementsCache.timestamp
            });
            return this.mediaElementsCache.elements;
        }

        this.performanceMetrics.cacheMisses++;

        const wrapped = wrapFunction(() => {
            const mainDocMedia = Array.from(document.querySelectorAll('video, audio'));
            const iframeMedia = this.getMediaFromIframes();

            // 合并并去重
            const allMedia = this.deduplicateMediaElements([...mainDocMedia, ...iframeMedia]);

            // 更新缓存
            this.updateCache(allMedia);

            // 更新性能指标
            const queryTime = performance.now() - startTime;
            this.updatePerformanceMetrics(queryTime);

            logDebug('媒体元素查询完成', {
                found: allMedia.length,
                queryTime: queryTime.toFixed(2) + 'ms',
                cacheVersion: this.mediaElementsCache.version
            });

            return allMedia;
        }, '获取所有媒体元素');

        return wrapped() || [];
    }

    /**
     * 检查缓存是否有效
     * @returns {boolean} 缓存是否有效
     */
    isCacheValid() {
        const cache = this.mediaElementsCache;

        // 基本检查
        if (cache.isStale || cache.elements.length === 0) {
            return false;
        }

        const now = Date.now();
        const age = now - cache.timestamp;

        // 自适应过期时间
        const maxAge = this.cacheConfig.adaptiveExpiry ?
            this.calculateAdaptiveExpiry() :
            this.cacheConfig.maxAge;

        if (age > maxAge) {
            return false;
        }

        // 检查DOM变化
        if (cache.lastMutationTime > cache.timestamp) {
            return false;
        }

        return true;
    }

    /**
     * 计算自适应过期时间
     * @returns {number} 过期时间（毫秒）
     */
    calculateAdaptiveExpiry() {
        const baseExpiry = this.cacheConfig.maxAge;
        const hitRate = this.performanceMetrics.cacheHits /
            (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses);

        // 命中率高的话延长缓存时间
        if (hitRate > 0.8) {
            return baseExpiry * 2;
        } else if (hitRate < 0.3) {
            return baseExpiry * 0.5;
        }

        return baseExpiry;
    }

    /**
     * 更新缓存
     * @param {Array} elements - 媒体元素数组
     */
    updateCache(elements) {
        const cache = this.mediaElementsCache;

        cache.elements = elements;
        cache.timestamp = Date.now();
        cache.isStale = false;
        cache.version++;

        // 限制缓存大小
        if (elements.length > this.cacheConfig.maxElements) {
            cache.elements = elements.slice(0, this.cacheConfig.maxElements);
            logWarn('缓存元素数量超限，已截断', {
                original: elements.length,
                cached: cache.elements.length
            });
        }

        // 设置缓存过期
        this.setCacheExpiration();
    }

    /**
     * 去重媒体元素
     * @param {Array} elements - 媒体元素数组
     * @returns {Array} 去重后的数组
     */
    deduplicateMediaElements(elements) {
        const seen = new Set();
        return elements.filter(element => {
            // 使用元素的唯一标识符进行去重
            const id = this.getElementId(element);
            if (seen.has(id)) {
                return false;
            }
            seen.add(id);
            return true;
        });
    }

    /**
     * 获取元素唯一标识符
     * @param {Element} element - 元素
     * @returns {string} 唯一标识符
     */
    getElementId(element) {
        // 尝试使用ID
        if (element.id) {
            return `id:${element.id}`;
        }

        // 使用src属性
        if (element.src) {
            return `src:${element.src}`;
        }

        // 使用元素在DOM中的位置
        const rect = element.getBoundingClientRect();
        return `pos:${rect.left},${rect.top},${rect.width},${rect.height}`;
    }

    /**
     * 更新性能指标
     * @param {number} queryTime - 查询时间
     */
    updatePerformanceMetrics(queryTime) {
        const metrics = this.performanceMetrics;
        metrics.averageQueryTime = (metrics.averageQueryTime + queryTime) / 2;

        // 定期输出性能报告
        const totalQueries = metrics.cacheHits + metrics.cacheMisses;
        if (totalQueries % 10 === 0) {
            logDebug('性能报告', {
                cacheHitRate: (metrics.cacheHits / totalQueries * 100).toFixed(1) + '%',
                averageQueryTime: metrics.averageQueryTime.toFixed(2) + 'ms',
                shadowDOMQueries: metrics.shadowDOMQueries
            });
        }
    }

    /**
     * 从iframe中获取媒体元素
     * @returns {Array} iframe中的媒体元素数组
     */
    getMediaFromIframes() {
        const iframeMedia = [];

        try {
            const iframes = Array.from(document.querySelectorAll('iframe'))
                .filter(iframe => isElementInViewport(iframe))
                .slice(0, 10); // 限制处理的iframe数量

            for (const iframe of iframes) {
                try {
                    if (iframe.contentDocument) {
                        const media = iframe.contentDocument.querySelectorAll('video, audio');
                        iframeMedia.push(...Array.from(media));
                    }
                } catch (e) {
                    // 跨域iframe无法访问，忽略错误
                    logDebug('无法访问iframe内容', { src: iframe.src });
                }
            }
        } catch (error) {
            handleError('获取iframe媒体失败', error);
        }

        return iframeMedia;
    }

    /**
     * 获取目标媒体元素
     * @returns {Element|null} 选中的媒体元素
     */
    getTargetMedia() {
        this.ensureFullFunctionalityInitialized();

        const wrapped = wrapFunction(() => {
            // 1. 检查lightbox模式中的视频
            const lightboxVideo = document.querySelector(`#${UI_CONFIG.LIGHTBOX_OVERLAY_ID} video`);
            if (lightboxVideo) {
                return lightboxVideo;
            }

            const allMedia = this.getAllMediaElements();
            if (allMedia.length === 0) return null;

            // 2. 检查鼠标悬停的媒体
            const hoveredMedia = allMedia.find(m => m.matches && m.matches(':hover'));
            if (hoveredMedia) return hoveredMedia;

            // 3. 检查最后活跃的媒体
            if (this.lastActiveMedia &&
                !this.lastActiveMedia.paused &&
                !this.lastActiveMedia.ended &&
                this.lastActiveMedia.readyState > 2) {
                if (document.body.contains(this.lastActiveMedia) ||
                    isInIframe(this.lastActiveMedia)) {
                    return this.lastActiveMedia;
                }
            }

            // 4. 选择视口内最大的媒体
            const visibleMedia = allMedia.filter(media => {
                return this.elementVisibilityMap.get(media) !== false &&
                    isElementInViewport(media);
            });

            if (visibleMedia.length > 0) {
                return this.getBiggestMedia(visibleMedia);
            }

            // 5. 返回第一个找到的媒体
            return allMedia[0];
        }, '获取目标媒体');

        return wrapped();
    }

    /**
     * 获取尺寸最大的媒体元素
     * @param {Array} mediaArray - 媒体元素数组
     * @returns {Element|null} 最大的媒体元素
     */
    getBiggestMedia(mediaArray) {
        if (!mediaArray || mediaArray.length === 0) return null;
        if (mediaArray.length === 1) return mediaArray[0];

        let biggestMedia = mediaArray[0];
        let maxArea = 0;

        for (const media of mediaArray) {
            let size = this.mediaSizeCache.get(media);
            if (!size) {
                size = getElementSize(media);
                this.mediaSizeCache.set(media, size);
            }

            if (size.area > maxArea) {
                maxArea = size.area;
                biggestMedia = media;
            }
        }

        return biggestMedia;
    }

    /**
     * 确保完整功能已初始化
     */
    ensureFullFunctionalityInitialized() {
        if (this.isFullFunctionalityInitialized) return;

        this.handleShadowDOMMedia();
        this.isFullFunctionalityInitialized = true;
    }

    /**
     * 设置缓存过期
     */
    setCacheExpiration() {
        clearTimeout(this.mediaElementsCache.timeoutId);
        this.mediaElementsCache.timeoutId = setTimeout(() => {
            this.invalidateCache();
        }, PERFORMANCE_CONFIG.CACHE_EXPIRE_TIME);
    }

    /**
     * 使缓存失效
     * @param {string} reason - 失效原因
     */
    invalidateCache(reason = 'unknown') {
        const cache = this.mediaElementsCache;

        logDebug('缓存失效', {
            reason,
            age: Date.now() - cache.timestamp,
            version: cache.version
        });

        cache.isStale = true;
        cache.lastMutationTime = Date.now();

        // 清理相关缓存
        this.mediaSizeCache = new WeakMap();

        // 清除缓存过期定时器
        if (cache.timeoutId) {
            clearTimeout(cache.timeoutId);
            cache.timeoutId = null;
        }
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 缓存统计
     */
    getCacheStats() {
        const cache = this.mediaElementsCache;
        const now = Date.now();

        return {
            isValid: this.isCacheValid(),
            age: now - cache.timestamp,
            version: cache.version,
            elementCount: cache.elements.length,
            shadowElementCount: cache.shadowElements.length,
            performanceMetrics: { ...this.performanceMetrics },
            lastMutationAge: now - cache.lastMutationTime
        };
    }

    /**
     * 清理过期缓存
     */
    cleanupExpiredCache() {
        if (!this.isCacheValid()) {
            this.invalidateCache('expired');
        }
    }

    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        // 定期清理过期缓存
        setInterval(() => {
            this.cleanupExpiredCache();
        }, 30000); // 每30秒检查一次

        // 定期输出性能报告
        setInterval(() => {
            const stats = this.getCacheStats();
            try {
                if (DEBUG_CONFIG && DEBUG_CONFIG.ENABLED && DEBUG_CONFIG.PERFORMANCE_MONITORING) {
                    console.group('📊 MediaDetector 性能报告');
                    console.log('缓存状态:', stats);
                    console.log('性能指标:', this.performanceMetrics);
                    console.groupEnd();
                }
            } catch (error) {
                console.warn('[MediaDetector] DEBUG_CONFIG访问失败，跳过性能报告', error);
            }
        }, 60000); // 每分钟输出一次报告
    }

    /**
     * 优化性能设置
     */
    optimizePerformance() {
        const metrics = this.performanceMetrics;
        const totalQueries = metrics.cacheHits + metrics.cacheMisses;

        if (totalQueries < 10) return; // 数据不足，不进行优化

        const hitRate = metrics.cacheHits / totalQueries;

        // 根据命中率调整缓存策略
        if (hitRate < 0.3) {
            // 命中率低，减少缓存时间
            this.cacheConfig.maxAge = Math.max(1000, this.cacheConfig.maxAge * 0.8);
            logDebug('性能优化：减少缓存时间', {
                newMaxAge: this.cacheConfig.maxAge,
                hitRate: (hitRate * 100).toFixed(1) + '%'
            });
        } else if (hitRate > 0.8) {
            // 命中率高，增加缓存时间
            this.cacheConfig.maxAge = Math.min(10000, this.cacheConfig.maxAge * 1.2);
            logDebug('性能优化：增加缓存时间', {
                newMaxAge: this.cacheConfig.maxAge,
                hitRate: (hitRate * 100).toFixed(1) + '%'
            });
        }

        // 根据查询时间调整Shadow DOM查询限制
        if (metrics.averageQueryTime > 50) {
            PERFORMANCE_CONFIG.SHADOW_DOM_QUERY_LIMIT = Math.max(10,
                PERFORMANCE_CONFIG.SHADOW_DOM_QUERY_LIMIT * 0.8);
            logDebug('性能优化：减少Shadow DOM查询限制', {
                newLimit: PERFORMANCE_CONFIG.SHADOW_DOM_QUERY_LIMIT,
                avgTime: metrics.averageQueryTime.toFixed(2) + 'ms'
            });
        }
    }

    /**
     * 重置性能指标
     */
    resetPerformanceMetrics() {
        this.performanceMetrics = {
            cacheHits: 0,
            cacheMisses: 0,
            shadowDOMQueries: 0,
            averageQueryTime: 0
        };
        logDebug('性能指标已重置');
    }

    /**
     * 清理资源
     */
    destroy() {
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }

        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
        }

        if (this.mediaCheckInterval) {
            clearInterval(this.mediaCheckInterval);
        }

        clearTimeout(this.mediaElementsCache.timeoutId);

        // 输出最终性能报告（安全检查）
        try {
            if (DEBUG_CONFIG && DEBUG_CONFIG.ENABLED) {
                console.log('📊 MediaDetector 最终性能报告:', {
                    cacheStats: this.getCacheStats(),
                    performanceMetrics: this.performanceMetrics
                });
            }
        } catch (error) {
            console.warn('[MediaDetector] DEBUG_CONFIG访问失败，跳过最终报告', error);
        }
    }
}

export default MediaDetector;
