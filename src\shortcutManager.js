/**
 * 快捷键管理模块
 * 负责快捷键的加载、存储、冲突检测和事件处理
 */

import { DEFAULT_SHORTCUTS, ERROR_MESSAGES, ERROR_SEVERITY, ERROR_TYPES } from './config.js';
import { logDebug, logInfo, formatShortcutDisplay, parseShortcutInput, checkAPISupport } from './utils.js';
import { handleError, wrapFunction } from './errorHandler.js';

/**
 * 快捷键管理器类
 */
class ShortcutManager {
    constructor() {
        this.shortcuts = {};
        this.keydownHandlers = [];
        this.isInitialized = false;
        this.degradedMode = false; // 是否运行在降级模式

        this.init();
    }

    /**
     * 初始化快捷键管理器
     */
    async init() {
        await this.loadShortcutSettings();
        this.setupStorageListener();
        this.setupKeyboardEventListeners();
        this.isInitialized = true;

        logInfo('快捷键管理器初始化完成', { shortcuts: this.shortcuts });
    }

    /**
     * 加载快捷键设置
     */
    async loadShortcutSettings() {
        const wrapped = wrapFunction(() => {
            return new Promise((resolve) => {
                // 检查Chrome存储API是否可用
                if (!checkAPISupport('chrome.storage')) {
                    logInfo('Chrome存储API不可用，使用默认快捷键设置');
                    this.shortcuts = DEFAULT_SHORTCUTS;
                    this.degradedMode = true;
                    resolve();
                    return;
                }

                try {
                    chrome.storage.sync.get({ shortcuts: DEFAULT_SHORTCUTS }, (data) => {
                        try {
                            this.shortcuts = data.shortcuts || DEFAULT_SHORTCUTS;
                            logDebug('快捷键设置已加载', this.shortcuts);
                            resolve();
                        } catch (error) {
                            handleError(ERROR_MESSAGES.SHORTCUT_LOAD_FAILED, error, {}, false, ERROR_SEVERITY.MEDIUM, ERROR_TYPES.INITIALIZATION);
                            this.shortcuts = DEFAULT_SHORTCUTS;
                            this.degradedMode = true;
                            resolve();
                        }
                    });
                } catch (error) {
                    logInfo('Chrome存储API访问失败，使用默认设置', error);
                    this.shortcuts = DEFAULT_SHORTCUTS;
                    this.degradedMode = true;
                    resolve();
                }
            });
        }, '加载快捷键设置');

        await wrapped();
    }

    /**
     * 保存快捷键设置
     * @param {Object} newShortcuts - 新的快捷键配置
     * @returns {Promise<boolean>} 保存是否成功
     */
    async saveShortcutSettings(newShortcuts) {
        const wrapped = wrapFunction(() => {
            return new Promise((resolve) => {
                // 验证快捷键
                const validationResult = this.validateShortcuts(newShortcuts);
                if (!validationResult.isValid) {
                    handleError('快捷键验证失败', new Error(validationResult.message), {}, true, ERROR_SEVERITY.MEDIUM, ERROR_TYPES.USER_INPUT);
                    resolve(false);
                    return;
                }

                // 在降级模式下，只更新内存中的设置
                if (this.degradedMode || !checkAPISupport('chrome.storage')) {
                    this.shortcuts = newShortcuts;
                    logInfo('快捷键设置已更新（降级模式）', newShortcuts);
                    resolve(true);
                    return;
                }

                try {
                    chrome.storage.sync.set({ shortcuts: newShortcuts }, () => {
                        if (chrome.runtime.lastError) {
                            handleError('保存快捷键失败', new Error(chrome.runtime.lastError.message), {}, true);
                            // 即使保存失败，也更新内存中的设置
                            this.shortcuts = newShortcuts;
                            this.degradedMode = true;
                            resolve(true);
                        } else {
                            this.shortcuts = newShortcuts;
                            logInfo('快捷键设置已保存', newShortcuts);
                            resolve(true);
                        }
                    });
                } catch (error) {
                    logInfo('Chrome存储API访问失败，使用降级模式', error);
                    this.shortcuts = newShortcuts;
                    this.degradedMode = true;
                    resolve(true);
                }
            });
        }, '保存快捷键设置');

        return await wrapped();
    }

    /**
     * 设置存储监听器
     */
    setupStorageListener() {
        // 在降级模式下跳过存储监听器设置
        if (this.degradedMode || !checkAPISupport('chrome.storage')) {
            logDebug('跳过存储监听器设置（降级模式）');
            return;
        }

        try {
            const wrapped = wrapFunction((changes, namespace) => {
                if (namespace === 'sync' && changes.shortcuts) {
                    this.shortcuts = changes.shortcuts.newValue || DEFAULT_SHORTCUTS;
                    logDebug('快捷键设置已更新', this.shortcuts);
                }
            }, '处理快捷键变更');

            chrome.storage.onChanged.addListener(wrapped);
        } catch (error) {
            logInfo('设置存储监听器失败，切换到降级模式', error);
            this.degradedMode = true;
        }
    }

    /**
     * 设置键盘事件监听器
     */
    setupKeyboardEventListeners() {
        const handleKeyDown = wrapFunction((event) => {
            this.handleKeyboardEvent(event);
        }, '处理键盘事件');

        // 在捕获阶段监听，确保能够拦截事件
        window.addEventListener('keydown', handleKeyDown, true);

        // 备用监听器，用于特殊情况
        document.addEventListener('keydown', (event) => {
            // 只在特定条件下使用备用处理器
            if (this.shouldUseBackupHandler(event)) {
                handleKeyDown(event);
            }
        }, true);
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyboardEvent(event) {
        if (!this.isInitialized) return;

        // 构建快捷键字符串
        const shortcutPressed = this.buildShortcutString(event);

        // 查找匹配的动作
        const action = this.findMatchingAction(shortcutPressed);

        if (!action) return;

        // 检查是否应该忽略此事件
        if (this.shouldIgnoreEvent(event)) return;

        // 通知所有注册的处理器
        const shouldPreventDefault = this.notifyHandlers(action, event);

        if (shouldPreventDefault) {
            event.preventDefault();
            event.stopPropagation();
            logDebug('快捷键已处理', { action, shortcut: shortcutPressed });
        }
    }

    /**
     * 构建快捷键字符串
     * @param {KeyboardEvent} event - 键盘事件
     * @returns {string} 快捷键字符串
     */
    buildShortcutString(event) {
        const parts = [];

        if (event.ctrlKey) parts.push('ctrl');
        if (event.altKey) parts.push('alt');
        if (event.shiftKey) parts.push('shift');
        if (event.metaKey) parts.push('meta');

        const key = event.key.toLowerCase();
        if (!['control', 'alt', 'shift', 'meta'].includes(key)) {
            parts.push(key);
        }

        return parts.join('+');
    }

    /**
     * 查找匹配的动作
     * @param {string} shortcutPressed - 按下的快捷键
     * @returns {string|null} 匹配的动作名称
     */
    findMatchingAction(shortcutPressed) {
        return Object.keys(this.shortcuts).find(action =>
            this.shortcuts[action] === shortcutPressed
        );
    }

    /**
     * 检查是否应该忽略事件
     * @param {KeyboardEvent} event - 键盘事件
     * @returns {boolean} 是否应该忽略
     */
    shouldIgnoreEvent(event) {
        // 在可编辑区域不拦截快捷键
        if (event.target.isContentEditable ||
            ['INPUT', 'TEXTAREA', 'SELECT'].includes(event.target.tagName)) {
            return true;
        }

        // 检查是否在特殊元素上
        if (event.target.closest('[contenteditable="true"]')) {
            return true;
        }

        // 检查是否应该让原生视频播放器处理
        if (this.shouldAllowNativeVideoControl(event)) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否应该允许原生视频播放器控制
     * @param {KeyboardEvent} event - 键盘事件
     * @returns {boolean} 是否应该允许原生控制
     */
    shouldAllowNativeVideoControl(event) {
        // 对于空格键，总是让原生视频播放器处理
        if (event.key === ' ' && !event.ctrlKey && !event.altKey && !event.shiftKey && !event.metaKey) {
            return true;
        }

        // 检查焦点是否在视频元素或其控制条上
        const target = event.target;
        if (target.tagName === 'VIDEO' || target.tagName === 'AUDIO') {
            // 如果焦点在媒体元素上，让原生控制处理常见的播放控制键
            const nativeKeys = [' ', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'k', 'm', 'f'];
            if (nativeKeys.includes(event.key) && !event.ctrlKey && !event.altKey && !event.shiftKey && !event.metaKey) {
                return true;
            }
        }

        // 检查是否在视频控制条或相关UI元素上
        if (target.closest('.video-controls, .plyr__controls, .vjs-control-bar, .ytp-chrome-bottom')) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否应该使用备用处理器
     * @param {KeyboardEvent} event - 键盘事件
     * @returns {boolean} 是否使用备用处理器
     */
    shouldUseBackupHandler(event) {
        // 在lightbox模式下使用备用处理器
        const lightboxOverlay = document.getElementById('vsc-lightbox-overlay');
        return lightboxOverlay && lightboxOverlay.style.display !== 'none';
    }

    /**
     * 通知所有处理器
     * @param {string} action - 动作名称
     * @param {KeyboardEvent} event - 键盘事件
     * @returns {boolean} 是否应该阻止默认行为
     */
    notifyHandlers(action, event) {
        let shouldPreventDefault = false;

        for (const handler of this.keydownHandlers) {
            try {
                const result = handler(action, event);
                if (result === true) {
                    shouldPreventDefault = true;
                }
            } catch (error) {
                handleError('快捷键处理器执行失败', error, { action });
            }
        }

        return shouldPreventDefault;
    }

    /**
     * 注册快捷键处理器
     * @param {Function} handler - 处理器函数
     */
    registerHandler(handler) {
        if (typeof handler === 'function') {
            this.keydownHandlers.push(handler);
            logDebug('快捷键处理器已注册');
        }
    }

    /**
     * 注销快捷键处理器
     * @param {Function} handler - 处理器函数
     */
    unregisterHandler(handler) {
        const index = this.keydownHandlers.indexOf(handler);
        if (index > -1) {
            this.keydownHandlers.splice(index, 1);
            logDebug('快捷键处理器已注销');
        }
    }

    /**
     * 验证快捷键配置
     * @param {Object} shortcuts - 快捷键配置
     * @returns {Object} 验证结果
     */
    validateShortcuts(shortcuts) {
        const result = {
            isValid: true,
            message: '',
            conflicts: [],
            errors: []
        };

        const shortcutMap = {};
        const requiredActions = Object.keys(DEFAULT_SHORTCUTS);

        // 检查必需的动作
        for (const action of requiredActions) {
            if (!shortcuts[action]) {
                result.errors.push(`缺少必需的快捷键: ${action}`);
            }
        }

        // 检查重复和冲突
        for (const [action, shortcut] of Object.entries(shortcuts)) {
            if (!shortcut || shortcut.trim() === '') {
                result.errors.push(`快捷键不能为空: ${action}`);
                continue;
            }

            if (shortcutMap[shortcut]) {
                result.conflicts.push({
                    shortcut,
                    actions: [shortcutMap[shortcut], action]
                });
            } else {
                shortcutMap[shortcut] = action;
            }
        }

        if (result.errors.length > 0 || result.conflicts.length > 0) {
            result.isValid = false;
            result.message = [
                ...result.errors,
                ...result.conflicts.map(c => `快捷键冲突: ${c.shortcut} (${c.actions.join(', ')})`)
            ].join('; ');
        }

        return result;
    }

    /**
     * 获取当前快捷键配置
     * @returns {Object} 当前快捷键配置
     */
    getShortcuts() {
        return { ...this.shortcuts };
    }

    /**
     * 获取格式化的快捷键显示
     * @returns {Object} 格式化后的快捷键配置
     */
    getFormattedShortcuts() {
        const formatted = {};
        for (const [action, shortcut] of Object.entries(this.shortcuts)) {
            formatted[action] = formatShortcutDisplay(shortcut);
        }
        return formatted;
    }

    /**
     * 重置为默认快捷键
     */
    async resetToDefaults() {
        await this.saveShortcutSettings(DEFAULT_SHORTCUTS);
        logInfo('快捷键已重置为默认值');
    }

    /**
     * 检查快捷键是否与浏览器快捷键冲突
     * @param {string} shortcut - 快捷键字符串
     * @returns {boolean} 是否冲突
     */
    checkBrowserConflict(shortcut) {
        const commonBrowserShortcuts = [
            'ctrl+t', 'ctrl+n', 'ctrl+w', 'ctrl+f', 'ctrl+s', 'ctrl+p', 'ctrl+r', 'f5',
            'alt+f4', 'ctrl+tab', 'ctrl+shift+tab', 'f1', 'f11', 'ctrl+shift+n',
            'ctrl+j', 'ctrl+h', 'ctrl+d', 'ctrl+l', 'ctrl+k',
            // Mac快捷键
            'meta+t', 'meta+n', 'meta+w', 'meta+f', 'meta+s', 'meta+p', 'meta+r',
            'meta+tab', 'meta+`', 'meta+shift+n', 'meta+j', 'meta+h', 'meta+d', 'meta+l', 'meta+k'
        ];

        return commonBrowserShortcuts.includes(shortcut.toLowerCase());
    }

    /**
     * 清理资源
     */
    destroy() {
        this.keydownHandlers = [];
        this.isInitialized = false;
    }
}

export default ShortcutManager;
