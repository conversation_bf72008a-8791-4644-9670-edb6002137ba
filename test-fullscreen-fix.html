<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页全屏功能修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-results {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        .video-container {
            margin: 20px 0;
            text-align: center;
        }
        
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background-color: #4caf50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
    </style>
</head>
<body>
    <h1>🔧 网页全屏功能修复测试</h1>
    
    <div class="test-section">
        <h3>🎯 修复内容</h3>
        <ul>
            <li>✅ 修复YouTube上网页全屏失效问题</li>
            <li>✅ 优化快捷键冲突处理逻辑</li>
            <li>✅ 确保插件快捷键优先于原生控制</li>
            <li>✅ 保持其他原生控制功能正常</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🚀 加载插件</h3>
        <p>首先加载视频速度控制器插件</p>
        
        <button onclick="loadPlugin()">加载插件</button>
        <button onclick="testPluginStatus()">检查插件状态</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div class="test-results" id="loadResults">
            点击"加载插件"开始测试...
        </div>
    </div>

    <div class="test-section">
        <h3>🎬 测试视频</h3>
        <p>使用以下快捷键测试功能：</p>
        <ul>
            <li><strong>f</strong> 键：网页全屏/退出全屏</li>
            <li><strong>=</strong> 键：增加播放速度</li>
            <li><strong>-</strong> 键：减少播放速度</li>
            <li><strong>0</strong> 键：重置播放速度</li>
            <li><strong>空格</strong> 键：播放/暂停（原生控制）</li>
        </ul>
        
        <div class="video-container">
            <video controls id="testVideo">
                <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                您的浏览器不支持视频标签。
            </video>
            <p>当前播放速度：<span id="speedDisplay">1.0x</span></p>
            <p>全屏状态：<span id="fullscreenStatus">普通模式</span></p>
        </div>
    </div>

    <div class="test-section">
        <h3>📊 功能测试</h3>
        <button onclick="testFullscreenFunction()">测试网页全屏功能</button>
        <button onclick="testSpeedControl()">测试速度控制</button>
        <button onclick="testKeyboardEvents()">测试键盘事件处理</button>
        
        <div class="test-results" id="testResults">
            点击测试按钮开始功能测试...
        </div>
    </div>

    <div class="test-section">
        <h3>🔍 实时监控</h3>
        <button onclick="startMonitoring()">开始监控</button>
        <button onclick="stopMonitoring()">停止监控</button>
        
        <div class="test-results" id="monitoringResults">
            监控未启动...
        </div>
    </div>

    <script>
        let monitoring = false;
        let monitoringInterval = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            return `[${timestamp}] ${prefix} ${message}\n`;
        }
        
        function clearResults() {
            document.getElementById('loadResults').textContent = '结果已清除';
            document.getElementById('testResults').textContent = '测试结果已清除';
            document.getElementById('monitoringResults').textContent = '监控未启动...';
        }
        
        async function loadPlugin() {
            const resultsDiv = document.getElementById('loadResults');
            let output = log('开始加载视频速度控制器插件...');
            resultsDiv.textContent = output;
            
            try {
                // 加载独立测试版本
                const script = document.createElement('script');
                script.src = './src/standalone-test.js';
                script.type = 'text/javascript';
                
                script.onload = function() {
                    output += log('插件加载成功', 'success');
                    resultsDiv.textContent = output;
                    
                    setTimeout(() => {
                        testPluginStatus();
                    }, 1000);
                };
                
                script.onerror = function(error) {
                    output += log('插件加载失败: ' + error.message, 'error');
                    resultsDiv.textContent = output;
                };
                
                document.head.appendChild(script);
                output += log('插件脚本已添加到页面');
                resultsDiv.textContent = output;
                
            } catch (error) {
                output += log('加载插件时发生错误: ' + error.message, 'error');
                resultsDiv.textContent = output;
            }
        }
        
        function testPluginStatus() {
            const resultsDiv = document.getElementById('loadResults');
            let output = resultsDiv.textContent + log('\n=== 插件状态检查 ===');
            
            try {
                const status = {
                    hasController: typeof window.videoSpeedController !== 'undefined',
                    isReady: window.videoSpeedController && window.videoSpeedController.isReady(),
                    hasShortcuts: window.videoSpeedController && window.videoSpeedController.getShortcuts
                };
                
                output += log('插件实例: ' + (status.hasController ? '存在' : '不存在'), 
                             status.hasController ? 'success' : 'error');
                output += log('插件就绪: ' + (status.isReady ? '是' : '否'), 
                             status.isReady ? 'success' : 'error');
                
                if (status.hasController && status.hasShortcuts) {
                    const shortcuts = window.videoSpeedController.getShortcuts();
                    output += log('快捷键配置: ' + JSON.stringify(shortcuts));
                    
                    if (shortcuts['toggle-fullscreen']) {
                        output += log('网页全屏快捷键: ' + shortcuts['toggle-fullscreen'], 'success');
                    } else {
                        output += log('网页全屏快捷键未配置', 'warning');
                    }
                }
                
            } catch (error) {
                output += log('状态检查失败: ' + error.message, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function testFullscreenFunction() {
            const resultsDiv = document.getElementById('testResults');
            let output = log('开始测试网页全屏功能...');
            resultsDiv.textContent = output;
            
            try {
                const video = document.getElementById('testVideo');
                const controller = window.videoSpeedController;
                
                if (!controller) {
                    output += log('插件未加载，无法测试', 'error');
                    resultsDiv.textContent = output;
                    return;
                }
                
                // 模拟按下f键
                const fKeyEvent = new KeyboardEvent('keydown', {
                    key: 'f',
                    code: 'KeyF',
                    bubbles: true,
                    cancelable: true
                });
                
                output += log('模拟按下f键...');
                document.dispatchEvent(fKeyEvent);
                
                // 检查全屏状态
                setTimeout(() => {
                    const isFullscreen = document.getElementById('vsc-lightbox-overlay') !== null;
                    output += log('全屏状态: ' + (isFullscreen ? '已进入网页全屏' : '未进入全屏'), 
                                 isFullscreen ? 'success' : 'warning');
                    
                    updateFullscreenStatus();
                    resultsDiv.textContent = output;
                    
                    if (isFullscreen) {
                        output += log('等待3秒后自动退出全屏...');
                        resultsDiv.textContent = output;
                        
                        setTimeout(() => {
                            document.dispatchEvent(fKeyEvent);
                            setTimeout(() => {
                                const stillFullscreen = document.getElementById('vsc-lightbox-overlay') !== null;
                                output += log('退出全屏状态: ' + (stillFullscreen ? '仍在全屏' : '已退出全屏'), 
                                             stillFullscreen ? 'warning' : 'success');
                                updateFullscreenStatus();
                                resultsDiv.textContent = output;
                            }, 500);
                        }, 3000);
                    }
                }, 500);
                
            } catch (error) {
                output += log('测试全屏功能失败: ' + error.message, 'error');
                resultsDiv.textContent = output;
            }
        }
        
        function testSpeedControl() {
            const resultsDiv = document.getElementById('testResults');
            let output = resultsDiv.textContent + log('\n=== 速度控制测试 ===');
            resultsDiv.textContent = output;
            
            const video = document.getElementById('testVideo');
            const originalSpeed = video.playbackRate;
            
            try {
                // 测试增速
                const increaseEvent = new KeyboardEvent('keydown', {
                    key: '=',
                    code: 'Equal',
                    bubbles: true,
                    cancelable: true
                });
                
                document.dispatchEvent(increaseEvent);
                
                setTimeout(() => {
                    const newSpeed = video.playbackRate;
                    output += log(`增速测试: ${originalSpeed}x → ${newSpeed}x`, 
                                 newSpeed > originalSpeed ? 'success' : 'error');
                    updateSpeedDisplay();
                    resultsDiv.textContent = output;
                    
                    // 测试重置
                    const resetEvent = new KeyboardEvent('keydown', {
                        key: '0',
                        code: 'Digit0',
                        bubbles: true,
                        cancelable: true
                    });
                    
                    document.dispatchEvent(resetEvent);
                    
                    setTimeout(() => {
                        const resetSpeed = video.playbackRate;
                        output += log(`重置测试: ${newSpeed}x → ${resetSpeed}x`, 
                                     resetSpeed === 1.0 ? 'success' : 'error');
                        updateSpeedDisplay();
                        resultsDiv.textContent = output;
                    }, 200);
                }, 200);
                
            } catch (error) {
                output += log('速度控制测试失败: ' + error.message, 'error');
                resultsDiv.textContent = output;
            }
        }
        
        function testKeyboardEvents() {
            const resultsDiv = document.getElementById('testResults');
            let output = resultsDiv.textContent + log('\n=== 键盘事件测试 ===');
            resultsDiv.textContent = output;
            
            // 监听键盘事件
            const eventListener = (event) => {
                if (['f', '=', '-', '0', ' '].includes(event.key)) {
                    output += log(`键盘事件: ${event.key} (${event.type})`);
                    resultsDiv.textContent = output;
                }
            };
            
            document.addEventListener('keydown', eventListener);
            
            output += log('键盘事件监听已启动，请按f、=、-、0或空格键测试');
            resultsDiv.textContent = output;
            
            // 10秒后移除监听器
            setTimeout(() => {
                document.removeEventListener('keydown', eventListener);
                output += log('键盘事件监听已停止', 'warning');
                resultsDiv.textContent = output;
            }, 10000);
        }
        
        function updateSpeedDisplay() {
            const video = document.getElementById('testVideo');
            const speedDisplay = document.getElementById('speedDisplay');
            if (video && speedDisplay) {
                speedDisplay.textContent = video.playbackRate.toFixed(1) + 'x';
            }
        }
        
        function updateFullscreenStatus() {
            const fullscreenStatus = document.getElementById('fullscreenStatus');
            const isFullscreen = document.getElementById('vsc-lightbox-overlay') !== null;
            if (fullscreenStatus) {
                fullscreenStatus.textContent = isFullscreen ? '网页全屏模式' : '普通模式';
                fullscreenStatus.style.color = isFullscreen ? '#4caf50' : '#666';
            }
        }
        
        function startMonitoring() {
            if (monitoring) return;
            
            monitoring = true;
            const resultsDiv = document.getElementById('monitoringResults');
            let output = log('开始实时状态监控...');
            resultsDiv.textContent = output;
            
            monitoringInterval = setInterval(() => {
                const video = document.getElementById('testVideo');
                const isFullscreen = document.getElementById('vsc-lightbox-overlay') !== null;
                const hasController = typeof window.videoSpeedController !== 'undefined';
                
                const status = {
                    speed: video ? video.playbackRate.toFixed(1) + 'x' : 'N/A',
                    fullscreen: isFullscreen ? '是' : '否',
                    controller: hasController ? '存在' : '无',
                    timestamp: new Date().toLocaleTimeString()
                };
                
                output += log(`状态: 速度=${status.speed}, 全屏=${status.fullscreen}, 控制器=${status.controller}`);
                resultsDiv.textContent = output;
                
                // 限制输出长度
                const lines = output.split('\n');
                if (lines.length > 15) {
                    output = lines.slice(-15).join('\n');
                    resultsDiv.textContent = output;
                }
            }, 2000);
        }
        
        function stopMonitoring() {
            monitoring = false;
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
            
            const resultsDiv = document.getElementById('monitoringResults');
            resultsDiv.textContent += log('监控已停止');
        }
        
        // 定期更新显示
        setInterval(() => {
            updateSpeedDisplay();
            updateFullscreenStatus();
        }, 1000);
        
        // 监听全屏状态变化
        const observer = new MutationObserver(() => {
            updateFullscreenStatus();
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
