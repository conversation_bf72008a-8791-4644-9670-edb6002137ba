/**
 * 内容脚本入口文件
 * 负责加载模块化的脚本
 */

// 使用type="module"的脚本无法直接作为content_script加载
// 因此我们需要动态创建并注入模块化脚本

(function() {
    'use strict';

    // 防止重复加载
    if (window.videoSpeedControllerLoading || window.videoSpeedController) {
        return;
    }

    window.videoSpeedControllerLoading = true;

    let retryCount = 0;
    const maxRetries = 3;
    const retryDelay = 1000; // 1秒

    function loadScript() {
        try {
            // 检查Chrome扩展API是否可用
            if (typeof chrome === 'undefined' || !chrome.runtime) {
                console.warn('Chrome扩展API不可用，延迟加载视频速度控制器');
                setTimeout(loadScript, retryDelay);
                return;
            }

            // 创建脚本元素
            const script = document.createElement('script');
            script.type = 'module';
            script.src = chrome.runtime.getURL('src/main.js');

            // 脚本加载成功
            script.onload = function() {
                console.log('视频速度控制器模块加载成功');
                script.remove();
                window.videoSpeedControllerLoading = false;
            };

            // 脚本加载失败
            script.onerror = function(error) {
                console.error('视频速度控制器模块加载失败:', error);
                script.remove();

                // 重试机制
                if (retryCount < maxRetries) {
                    retryCount++;
                    console.log(`重试加载视频速度控制器 (${retryCount}/${maxRetries})`);
                    setTimeout(loadScript, retryDelay * retryCount);
                } else {
                    console.error('视频速度控制器加载失败，已达到最大重试次数');
                    window.videoSpeedControllerLoading = false;

                    // 显示用户友好的错误消息
                    showLoadingError();
                }
            };

            // 添加到页面
            const target = document.head || document.documentElement;
            if (target) {
                target.appendChild(script);
            } else {
                // DOM还未准备好，等待
                document.addEventListener('DOMContentLoaded', () => {
                    (document.head || document.documentElement).appendChild(script);
                });
            }

        } catch (error) {
            console.error('创建脚本元素失败:', error);
            window.videoSpeedControllerLoading = false;
        }
    }

    function showLoadingError() {
        try {
            // 创建安全的错误提示（避免TrustedHTML问题）
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: rgba(220, 53, 69, 0.9);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                z-index: 2147483647;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            // 安全地创建标题元素
            const titleDiv = document.createElement('div');
            titleDiv.style.cssText = 'font-weight: bold; margin-bottom: 4px;';
            titleDiv.textContent = '视频速度控制器';
            errorDiv.appendChild(titleDiv);

            // 安全地创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = 'font-size: 12px;';
            messageDiv.textContent = '加载失败，请刷新页面重试';
            errorDiv.appendChild(messageDiv);

            if (document.body) {
                document.body.appendChild(errorDiv);
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 5000);
            }
        } catch (error) {
            console.error('显示加载错误失败:', error);
        }
    }

    // 开始加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadScript);
    } else {
        loadScript();
    }
})();
