/**
 * 内容脚本入口文件
 * 负责加载模块化的脚本
 */

// 使用type="module"的脚本无法直接作为content_script加载
// 因此我们需要动态创建并注入模块化脚本

(function() {
    'use strict';

    // 防止重复加载
    if (window.videoSpeedControllerLoading || window.videoSpeedController) {
        return;
    }

    window.videoSpeedControllerLoading = true;

    let retryCount = 0;
    const maxRetries = 3;
    const retryDelay = 1000; // 1秒
    const maxWaitTime = 10000; // 最大等待时间10秒
    const startTime = Date.now();

    /**
     * 检查是否在Chrome扩展环境中
     * @returns {boolean} 是否在扩展环境中
     */
    function isExtensionEnvironment() {
        return typeof chrome !== 'undefined' &&
               chrome.runtime &&
               typeof chrome.runtime.getURL === 'function';
    }

    /**
     * 获取脚本URL
     * @returns {string} 脚本URL
     */
    function getScriptURL() {
        if (isExtensionEnvironment()) {
            return chrome.runtime.getURL('src/main.js');
        } else {
            // 在非扩展环境下使用相对路径
            // 对于file://协议，直接使用相对路径
            return './src/main.js';
        }
    }

    function loadScript() {
        try {
            const currentTime = Date.now();
            const elapsedTime = currentTime - startTime;

            // 检查Chrome扩展API是否可用
            if (!isExtensionEnvironment()) {
                // 如果等待时间超过最大等待时间，启动降级模式
                if (elapsedTime > maxWaitTime) {
                    console.warn('Chrome扩展API等待超时，启动降级模式');
                    loadScriptInDegradedMode();
                    return;
                } else if (retryCount < maxRetries) {
                    console.warn('Chrome扩展API不可用，延迟加载视频速度控制器');
                    retryCount++;
                    setTimeout(loadScript, retryDelay);
                    return;
                } else {
                    console.warn('Chrome扩展API重试次数已达上限，启动降级模式');
                    loadScriptInDegradedMode();
                    return;
                }
            }

            // 创建脚本元素
            const script = document.createElement('script');
            script.type = 'module';
            script.src = getScriptURL();

            // 脚本加载成功
            script.onload = function() {
                console.log('视频速度控制器模块加载成功');
                script.remove();
                window.videoSpeedControllerLoading = false;
            };

            // 脚本加载失败
            script.onerror = function(error) {
                console.error('视频速度控制器模块加载失败:', error);
                script.remove();

                // 重试机制
                if (retryCount < maxRetries) {
                    retryCount++;
                    console.log(`重试加载视频速度控制器 (${retryCount}/${maxRetries})`);
                    setTimeout(loadScript, retryDelay * retryCount);
                } else {
                    console.error('视频速度控制器加载失败，已达到最大重试次数');
                    window.videoSpeedControllerLoading = false;

                    // 显示用户友好的错误消息
                    showLoadingError();
                }
            };

            // 添加到页面
            const target = document.head || document.documentElement;
            if (target) {
                target.appendChild(script);
            } else {
                // DOM还未准备好，等待
                document.addEventListener('DOMContentLoaded', () => {
                    (document.head || document.documentElement).appendChild(script);
                });
            }

        } catch (error) {
            console.error('创建脚本元素失败:', error);
            window.videoSpeedControllerLoading = false;
        }
    }

    /**
     * 在降级模式下加载脚本
     */
    function loadScriptInDegradedMode() {
        try {
            console.log('启动视频速度控制器降级模式');

            // 设置降级模式标志
            window.videoSpeedControllerDegradedMode = true;

            // 创建脚本元素
            const script = document.createElement('script');
            script.type = 'module';
            script.src = getScriptURL();

            // 脚本加载成功
            script.onload = function() {
                console.log('视频速度控制器模块加载成功（降级模式）');
                script.remove();
                window.videoSpeedControllerLoading = false;
            };

            // 脚本加载失败
            script.onerror = function(error) {
                console.error('视频速度控制器模块加载失败（降级模式）:', error);
                script.remove();
                window.videoSpeedControllerLoading = false;
                showLoadingError();
            };

            // 添加到页面
            const target = document.head || document.documentElement;
            if (target) {
                target.appendChild(script);
            } else {
                // DOM还未准备好，等待
                document.addEventListener('DOMContentLoaded', () => {
                    (document.head || document.documentElement).appendChild(script);
                });
            }

        } catch (error) {
            console.error('降级模式加载失败:', error);
            window.videoSpeedControllerLoading = false;
            showLoadingError();
        }
    }

    function showLoadingError() {
        try {
            // 创建安全的错误提示（避免TrustedHTML问题）
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: rgba(220, 53, 69, 0.9);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                z-index: 2147483647;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            // 安全地创建标题元素
            const titleDiv = document.createElement('div');
            titleDiv.style.cssText = 'font-weight: bold; margin-bottom: 4px;';
            titleDiv.textContent = '视频速度控制器';
            errorDiv.appendChild(titleDiv);

            // 安全地创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = 'font-size: 12px;';
            messageDiv.textContent = '加载失败，请刷新页面重试';
            errorDiv.appendChild(messageDiv);

            if (document.body) {
                document.body.appendChild(errorDiv);
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 5000);
            }
        } catch (error) {
            console.error('显示加载错误失败:', error);
        }
    }

    // 开始加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadScript);
    } else {
        loadScript();
    }
})();
