<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome扩展程序修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .error-box {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-results {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1976d2;
        }
        
        .video-container {
            margin: 20px 0;
            text-align: center;
        }
        
        video {
            width: 100%;
            max-width: 600px;
            height: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Chrome扩展程序修复测试</h1>
    
    <div class="test-section">
        <h3>📋 修复内容</h3>
        <ul>
            <li>✅ 修复Chrome扩展API兼容性问题</li>
            <li>✅ 解决视频速度控制器加载延迟问题</li>
            <li>✅ 实现降级模式支持</li>
            <li>✅ 改进错误处理和恢复机制</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🚀 手动加载插件</h3>
        <p>在file://环境下手动加载content script来测试修复效果</p>
        
        <button onclick="loadContentScript()">加载Content Script</button>
        <button onclick="loadStandaloneVersion()">加载独立测试版本</button>
        <button onclick="testPluginStatus()">检查插件状态</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div class="test-results" id="loadResults">
            点击"加载Content Script"开始测试...
        </div>
    </div>

    <div class="test-section">
        <h3>🎬 测试视频</h3>
        <p>加载插件后，使用以下快捷键测试功能：</p>
        <ul>
            <li><strong>=</strong> 键：增加播放速度</li>
            <li><strong>-</strong> 键：减少播放速度</li>
            <li><strong>0</strong> 键：重置播放速度</li>
        </ul>
        
        <div class="video-container">
            <video controls id="testVideo">
                <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                您的浏览器不支持视频标签。
            </video>
            <p>当前播放速度：<span id="speedDisplay">1.0x</span></p>
        </div>
    </div>

    <div class="test-section">
        <h3>📊 实时状态监控</h3>
        <button onclick="startMonitoring()">开始监控</button>
        <button onclick="stopMonitoring()">停止监控</button>
        
        <div class="test-results" id="monitoringResults">
            监控未启动...
        </div>
    </div>

    <script>
        let monitoring = false;
        let monitoringInterval = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            return `[${timestamp}] ${prefix} ${message}\n`;
        }
        
        function clearResults() {
            document.getElementById('loadResults').textContent = '结果已清除';
            document.getElementById('monitoringResults').textContent = '监控未启动...';
        }
        
        async function loadContentScript() {
            const resultsDiv = document.getElementById('loadResults');
            let output = log('开始手动加载Content Script...');
            resultsDiv.textContent = output;
            
            try {
                // 创建并加载content script
                const script = document.createElement('script');
                script.src = './src/content.js';
                script.type = 'text/javascript';
                
                script.onload = function() {
                    output += log('Content Script加载成功', 'success');
                    resultsDiv.textContent = output;
                    
                    // 等待一段时间让插件初始化
                    setTimeout(() => {
                        testPluginStatus();
                    }, 2000);
                };
                
                script.onerror = function(error) {
                    output += log('Content Script加载失败: ' + error.message, 'error');
                    resultsDiv.textContent = output;
                };
                
                document.head.appendChild(script);
                output += log('Content Script已添加到页面');
                resultsDiv.textContent = output;
                
            } catch (error) {
                output += log('加载Content Script时发生错误: ' + error.message, 'error');
                resultsDiv.textContent = output;
            }
        }

        async function loadStandaloneVersion() {
            const resultsDiv = document.getElementById('loadResults');
            let output = log('开始加载独立测试版本...');
            resultsDiv.textContent = output;

            try {
                // 创建并加载独立测试脚本
                const script = document.createElement('script');
                script.src = './src/standalone-test.js';
                script.type = 'text/javascript';

                script.onload = function() {
                    output += log('独立测试版本加载成功', 'success');
                    resultsDiv.textContent = output;

                    // 等待一段时间让插件初始化
                    setTimeout(() => {
                        testPluginStatus();
                    }, 1000);
                };

                script.onerror = function(error) {
                    output += log('独立测试版本加载失败: ' + error.message, 'error');
                    resultsDiv.textContent = output;
                };

                document.head.appendChild(script);
                output += log('独立测试脚本已添加到页面');
                resultsDiv.textContent = output;

            } catch (error) {
                output += log('加载独立测试版本时发生错误: ' + error.message, 'error');
                resultsDiv.textContent = output;
            }
        }

        function testPluginStatus() {
            const resultsDiv = document.getElementById('loadResults');
            let output = resultsDiv.textContent + log('\n=== 插件状态检查 ===');
            
            try {
                // 检查各种状态
                const status = {
                    hasVideoSpeedController: typeof window.videoSpeedController !== 'undefined',
                    hasVideoSpeedControllerLoading: typeof window.videoSpeedControllerLoading !== 'undefined',
                    hasVideoSpeedControllerDegradedMode: typeof window.videoSpeedControllerDegradedMode !== 'undefined',
                    videoSpeedControllerLoading: window.videoSpeedControllerLoading,
                    videoSpeedControllerDegradedMode: window.videoSpeedControllerDegradedMode,
                    hasChromeAPI: typeof chrome !== 'undefined',
                    hasChromeRuntime: typeof chrome !== 'undefined' && typeof chrome.runtime !== 'undefined'
                };
                
                output += log('插件实例: ' + (status.hasVideoSpeedController ? '存在' : '不存在'), 
                             status.hasVideoSpeedController ? 'success' : 'error');
                output += log('加载状态: ' + (status.videoSpeedControllerLoading ? '加载中' : '未加载'));
                output += log('降级模式: ' + (status.videoSpeedControllerDegradedMode ? '是' : '否'));
                output += log('Chrome API: ' + (status.hasChromeAPI ? '存在' : '不存在'));
                output += log('Chrome Runtime: ' + (status.hasChromeRuntime ? '存在' : '不存在'));
                
                if (status.hasVideoSpeedController) {
                    const controller = window.videoSpeedController;
                    output += log('插件就绪状态: ' + (controller.isReady ? controller.isReady() : '未知'));
                    output += log('降级模式状态: ' + (controller.degradedMode ? '是' : '否'));
                    
                    // 测试各个模块
                    const modules = ['mediaDetector', 'shortcutManager', 'playbackController', 'uiManager'];
                    modules.forEach(moduleName => {
                        if (controller[moduleName]) {
                            output += log(`${moduleName}: 已初始化`, 'success');
                        } else {
                            output += log(`${moduleName}: 未初始化`, 'warning');
                        }
                    });
                }
                
            } catch (error) {
                output += log('状态检查失败: ' + error.message, 'error');
            }
            
            resultsDiv.textContent = output;
        }
        
        function startMonitoring() {
            if (monitoring) return;
            
            monitoring = true;
            const resultsDiv = document.getElementById('monitoringResults');
            let output = log('开始实时状态监控...');
            resultsDiv.textContent = output;
            
            monitoringInterval = setInterval(() => {
                const status = {
                    hasController: typeof window.videoSpeedController !== 'undefined',
                    isLoading: window.videoSpeedControllerLoading,
                    isDegraded: window.videoSpeedControllerDegradedMode,
                    timestamp: new Date().toLocaleTimeString()
                };
                
                output += log(`状态更新: 控制器=${status.hasController ? '存在' : '无'}, 加载中=${status.isLoading ? '是' : '否'}, 降级=${status.isDegraded ? '是' : '否'}`);
                resultsDiv.textContent = output;
                
                // 限制输出长度
                const lines = output.split('\n');
                if (lines.length > 20) {
                    output = lines.slice(-20).join('\n');
                    resultsDiv.textContent = output;
                }
            }, 2000);
        }
        
        function stopMonitoring() {
            monitoring = false;
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
            
            const resultsDiv = document.getElementById('monitoringResults');
            resultsDiv.textContent += log('监控已停止');
        }
        
        // 监听键盘事件来测试快捷键
        document.addEventListener('keydown', function(event) {
            if (['=', '-', '0'].includes(event.key)) {
                const resultsDiv = document.getElementById('monitoringResults');
                resultsDiv.textContent += log(`快捷键测试: ${event.key} 键被按下`);

                // 更新速度显示
                setTimeout(() => {
                    updateSpeedDisplay();
                }, 100);
            }
        });

        // 更新速度显示
        function updateSpeedDisplay() {
            const video = document.getElementById('testVideo');
            const speedDisplay = document.getElementById('speedDisplay');
            if (video && speedDisplay) {
                speedDisplay.textContent = video.playbackRate.toFixed(1) + 'x';
            }
        }

        // 定期更新速度显示
        setInterval(updateSpeedDisplay, 1000);
    </script>
</body>
</html>
