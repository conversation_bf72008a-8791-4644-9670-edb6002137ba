/**
 * 工具函数模块
 * 包含通用的工具函数和辅助方法
 */

import { PERFORMANCE_CONFIG, DEBUG_CONFIG } from './config.js';

/**
 * 增强的防抖函数，支持立即执行选项
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function enhancedDebounce(func, wait, immediate = false) {
    let timeout;
    return function(...args) {
        const context = this;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

/**
 * 标准防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = PERFORMANCE_CONFIG.DEBOUNCE_DELAY) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 检查元素是否在视口内
 * @param {Element} el - 要检查的元素
 * @returns {boolean} 是否在视口内
 */
export function isElementInViewport(el) {
    try {
        if (!el || !el.getBoundingClientRect) return false;
        
        const rect = el.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;
        const windowWidth = window.innerWidth || document.documentElement.clientWidth;
        
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= windowHeight &&
            rect.right <= windowWidth &&
            rect.width > 0 &&
            rect.height > 0
        );
    } catch (error) {
        logError('检查元素可见性失败', error);
        return false;
    }
}

/**
 * 检查元素是否在iframe中
 * @param {Element} el - 要检查的元素
 * @returns {boolean} 是否在iframe中
 */
export function isInIframe(el) {
    try {
        if (!el) return false;
        
        // 检查元素的ownerDocument是否与当前document不同
        return el.ownerDocument !== document;
    } catch (error) {
        logError('检查iframe状态失败', error);
        return false;
    }
}

/**
 * 获取元素的尺寸信息
 * @param {Element} el - 要获取尺寸的元素
 * @returns {Object} 包含width和height的对象
 */
export function getElementSize(el) {
    try {
        if (!el || !el.getBoundingClientRect) {
            return { width: 0, height: 0 };
        }
        
        const rect = el.getBoundingClientRect();
        return {
            width: rect.width,
            height: rect.height,
            area: rect.width * rect.height
        };
    } catch (error) {
        logError('获取元素尺寸失败', error);
        return { width: 0, height: 0, area: 0 };
    }
}

/**
 * 安全地执行函数，捕获并记录错误
 * @param {Function} func - 要执行的函数
 * @param {string} context - 执行上下文描述
 * @param {...any} args - 函数参数
 * @returns {any} 函数执行结果，出错时返回null
 */
export function safeExecute(func, context, ...args) {
    try {
        return func(...args);
    } catch (error) {
        logError(`${context}执行失败`, error);
        return null;
    }
}

/**
 * 创建一个带有错误处理的Promise包装器
 * @param {Function} asyncFunc - 异步函数
 * @param {string} context - 执行上下文
 * @returns {Promise} 包装后的Promise
 */
export function safeAsync(asyncFunc, context) {
    return async (...args) => {
        try {
            return await asyncFunc(...args);
        } catch (error) {
            logError(`${context}异步执行失败`, error);
            return null;
        }
    };
}

/**
 * 日志记录函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据
 */
export function log(level, message, data = null) {
    if (!DEBUG_CONFIG.ENABLED) return;
    
    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(DEBUG_CONFIG.LOG_LEVEL);
    const messageLevelIndex = levels.indexOf(level);
    
    if (messageLevelIndex >= currentLevelIndex) {
        const timestamp = new Date().toISOString();
        const prefix = `[VSC ${timestamp}]`;
        
        switch (level) {
            case 'debug':
                console.debug(prefix, message, data);
                break;
            case 'info':
                console.info(prefix, message, data);
                break;
            case 'warn':
                console.warn(prefix, message, data);
                break;
            case 'error':
                console.error(prefix, message, data);
                break;
        }
    }
}

/**
 * 错误日志记录（带后备方案）
 * @param {string} message - 错误消息
 * @param {Error} error - 错误对象
 */
export function logError(message, error) {
    try {
        log('error', message, error);
    } catch (logErr) {
        // 后备方案：直接使用console.error
        console.error('[VSC Error]', message, error, logErr);
    }
}

/**
 * 警告日志记录（带后备方案）
 * @param {string} message - 警告消息
 * @param {any} data - 附加数据
 */
export function logWarn(message, data) {
    try {
        log('warn', message, data);
    } catch (logErr) {
        console.warn('[VSC Warn]', message, data, logErr);
    }
}

/**
 * 信息日志记录（带后备方案）
 * @param {string} message - 信息消息
 * @param {any} data - 附加数据
 */
export function logInfo(message, data) {
    try {
        log('info', message, data);
    } catch (logErr) {
        console.info('[VSC Info]', message, data, logErr);
    }
}

/**
 * 调试日志记录（带后备方案）
 * @param {string} message - 调试消息
 * @param {any} data - 附加数据
 */
export function logDebug(message, data) {
    try {
        log('debug', message, data);
    } catch (logErr) {
        console.debug('[VSC Debug]', message, data, logErr);
    }
}

/**
 * 检查浏览器API支持
 * @param {string} apiName - API名称
 * @returns {boolean} 是否支持
 */
export function checkAPISupport(apiName) {
    try {
        switch (apiName) {
            case 'MutationObserver':
                return typeof MutationObserver !== 'undefined';
            case 'IntersectionObserver':
                return typeof IntersectionObserver !== 'undefined';
            case 'WeakMap':
                return typeof WeakMap !== 'undefined';
            case 'chrome.storage':
                return checkChromeStorageAPI();
            case 'requestIdleCallback':
                return typeof requestIdleCallback !== 'undefined';
            case 'ResizeObserver':
                return typeof ResizeObserver !== 'undefined';
            default:
                return false;
        }
    } catch (error) {
        logError(`检查API支持失败: ${apiName}`, error);
        return false;
    }
}

/**
 * 检查Chrome存储API是否可用
 * @returns {boolean} 是否可用
 */
function checkChromeStorageAPI() {
    try {
        // 检查基本的chrome对象
        if (typeof chrome === 'undefined') {
            return false;
        }

        // 检查storage API
        if (!chrome.storage) {
            return false;
        }

        // 检查sync存储
        if (!chrome.storage.sync) {
            return false;
        }

        // 尝试访问storage API的方法
        if (typeof chrome.storage.sync.get !== 'function' ||
            typeof chrome.storage.sync.set !== 'function') {
            return false;
        }

        return true;
    } catch (error) {
        logError('Chrome存储API检查失败', error);
        return false;
    }
}

/**
 * 格式化快捷键显示
 * @param {string} shortcut - 快捷键字符串
 * @returns {string} 格式化后的显示文本
 */
export function formatShortcutDisplay(shortcut) {
    if (shortcut === ' ') return '空格键';
    if (shortcut === 'escape') return 'ESC';
    return shortcut;
}

/**
 * 解析快捷键输入
 * @param {string} displayValue - 显示值
 * @returns {string} 实际快捷键值
 */
export function parseShortcutInput(displayValue) {
    if (displayValue === '空格键') return ' ';
    if (displayValue === 'ESC') return 'escape';
    return displayValue;
}

/**
 * 生成唯一ID
 * @param {string} prefix - 前缀
 * @returns {string} 唯一ID
 */
export function generateUniqueId(prefix = 'vsc') {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 安全地创建带有文本内容的DOM元素（避免TrustedHTML问题）
 * @param {string} tagName - 标签名
 * @param {Object} options - 选项
 * @param {string} options.textContent - 文本内容
 * @param {string} options.className - CSS类名
 * @param {Object} options.style - 样式对象
 * @param {Object} options.attributes - 属性对象
 * @returns {HTMLElement} 创建的元素
 */
export function createSafeElement(tagName, options = {}) {
    try {
        const element = document.createElement(tagName);

        if (options.textContent) {
            element.textContent = options.textContent;
        }

        if (options.className) {
            element.className = options.className;
        }

        if (options.style) {
            if (typeof options.style === 'string') {
                element.style.cssText = options.style;
            } else {
                Object.assign(element.style, options.style);
            }
        }

        if (options.attributes) {
            Object.entries(options.attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });
        }

        return element;
    } catch (error) {
        logError('创建安全元素失败', error);
        return document.createElement('div');
    }
}

/**
 * 安全地创建通知元素
 * @param {string} title - 标题
 * @param {string} message - 消息
 * @param {string} type - 类型 ('error', 'warning', 'success', 'info')
 * @returns {HTMLElement} 通知元素
 */
export function createSafeNotification(title, message, type = 'info') {
    const colors = {
        error: 'rgba(220, 53, 69, 0.9)',
        warning: 'rgba(255, 193, 7, 0.9)',
        success: 'rgba(40, 167, 69, 0.9)',
        info: 'rgba(23, 162, 184, 0.9)'
    };

    const textColors = {
        error: 'white',
        warning: '#212529',
        success: 'white',
        info: 'white'
    };

    const container = createSafeElement('div', {
        style: `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: ${colors[type] || colors.info};
            color: ${textColors[type] || textColors.info};
            padding: 12px 16px;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            z-index: 2147483647;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `
    });

    if (title) {
        const titleElement = createSafeElement('div', {
            textContent: title,
            style: 'font-weight: bold; margin-bottom: 4px;'
        });
        container.appendChild(titleElement);
    }

    if (message) {
        const messageElement = createSafeElement('div', {
            textContent: message,
            style: 'font-size: 12px;'
        });
        container.appendChild(messageElement);
    }

    return container;
}
