/**
 * 视频速度控制器配置文件
 * 包含所有常量、默认值和配置选项
 */

// 默认快捷键配置
export const DEFAULT_SHORTCUTS = {
    increase: '=',
    decrease: '-',
    reset: '0',
    'toggle-fullscreen': 'f',
};

// 性能相关配置
export const PERFORMANCE_CONFIG = {
    // 缓存过期时间（毫秒）
    CACHE_EXPIRE_TIME: 2000,

    // 媒体检测间隔配置
    MEDIA_CHECK_INTERVALS: [100, 200, 500, 1000, 2000, 5000],

    // 防抖延迟时间
    DEBOUNCE_DELAY: 100,

    // Shadow DOM查询限制数量
    SHADOW_DOM_QUERY_LIMIT: 100,

    // 指示器显示时间
    INDICATOR_DISPLAY_TIME: 1500,
};

// UI相关配置
export const UI_CONFIG = {
    // 指示器元素ID
    INDICATOR_ID: 'video-speed-indicator',

    // 全屏覆盖层ID
    LIGHTBOX_OVERLAY_ID: 'vsc-lightbox-overlay',

    // CSS类名
    CSS_CLASSES: {
        LIGHTBOX_VIDEO: 'vsc-lightbox-video',
        BODY_LOCK: 'vsc-body-lock',
        INDICATOR_VISIBLE: 'visible',
        MEDIA_HIGHLIGHT: 'vsc-media-highlight',
    },

    // Z-index值
    Z_INDEX: {
        INDICATOR: 2147483647,
        LIGHTBOX: 2147483646,
        MEDIA_HIGHLIGHT: 2147483645,
    },
};

// 媒体控制相关配置
export const MEDIA_CONFIG = {
    // 速度调整步长
    SPEED_STEP: 0.1,

    // 最小/最大播放速度
    MIN_SPEED: 0.1,
    MAX_SPEED: 5.0,

    // 默认播放速度
    DEFAULT_SPEED: 1.0,

    // 快进/快退步长（秒）
    SEEK_STEP: 5,

    // 音量调整步长
    VOLUME_STEP: 0.1,
};

// 错误消息配置
export const ERROR_MESSAGES = {
    SHORTCUT_LOAD_FAILED: '加载快捷键失败',
    SHORTCUT_CHANGE_FAILED: '处理快捷键变更失败',
    KEYBOARD_EVENT_FAILED: '处理键盘事件失败',
    MEDIA_DETECTION_FAILED: '检查媒体元素失败',
    SHADOW_DOM_QUERY_FAILED: 'Shadow DOM查询失败',
    PLAYBACK_CONTROL_FAILED: '播放控制失败',
    INDICATOR_SHOW_FAILED: '显示指示器失败',
    LIGHTBOX_TOGGLE_FAILED: '切换全屏模式失败',
    MODULE_INIT_FAILED: '模块初始化失败',
    API_NOT_SUPPORTED: 'API不支持',
    STORAGE_ACCESS_FAILED: '存储访问失败',
    DOM_MANIPULATION_FAILED: 'DOM操作失败',
    EVENT_HANDLER_FAILED: '事件处理失败',
    CACHE_OPERATION_FAILED: '缓存操作失败',
};

// 错误严重程度
export const ERROR_SEVERITY = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
};

// 错误类型
export const ERROR_TYPES = {
    INITIALIZATION: 'initialization',
    RUNTIME: 'runtime',
    USER_INPUT: 'user_input',
    NETWORK: 'network',
    PERMISSION: 'permission',
    COMPATIBILITY: 'compatibility'
};

// 调试配置
export const DEBUG_CONFIG = {
    // 是否启用调试模式
    ENABLED: true,

    // 日志级别
    LOG_LEVEL: 'debug', // 'debug', 'info', 'warn', 'error'

    // 性能监控
    PERFORMANCE_MONITORING: false,
};

// 浏览器兼容性配置
export const BROWSER_CONFIG = {
    // 支持的API检查
    REQUIRED_APIS: [
        'MutationObserver',
        'IntersectionObserver',
        'WeakMap',
        'chrome.storage',
    ],

    // 可选API
    OPTIONAL_APIS: [
        'requestIdleCallback',
        'ResizeObserver',
    ],
};

// 媒体选择优先级配置
export const MEDIA_SELECTION_PRIORITY = {
    // 优先级权重
    LIGHTBOX_VIDEO: 1000,
    HOVERED_MEDIA: 900,
    LAST_ACTIVE_PLAYING: 800,
    LARGEST_IN_VIEWPORT: 700,
    FIRST_FOUND: 100,
};

// 导出所有配置的合并对象
export const CONFIG = {
    DEFAULT_SHORTCUTS,
    PERFORMANCE_CONFIG,
    UI_CONFIG,
    MEDIA_CONFIG,
    ERROR_MESSAGES,
    ERROR_SEVERITY,
    ERROR_TYPES,
    DEBUG_CONFIG,
    BROWSER_CONFIG,
    MEDIA_SELECTION_PRIORITY,
};

export default CONFIG;
