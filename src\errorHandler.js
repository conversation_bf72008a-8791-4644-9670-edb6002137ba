/**
 * 错误处理模块
 * 统一的错误处理、日志记录和用户通知机制
 */

import { ERROR_MESSAGES, ERROR_SEVERITY, ERROR_TYPES, DEBUG_CONFIG, UI_CONFIG } from './config.js';
import { logError, logWarn, generateUniqueId } from './utils.js';

/**
 * 错误处理器类
 */
class ErrorHandler {
    constructor() {
        this.errorCount = 0;
        this.errorHistory = [];
        this.maxHistorySize = 50;
        this.userNotificationElement = null;
        this.init();
    }

    /**
     * 初始化错误处理器
     */
    init() {
        // 创建用户通知元素
        this.createNotificationElement();

        // 监听全局错误
        this.setupGlobalErrorHandling();
    }

    /**
     * 创建用户通知元素
     */
    createNotificationElement() {
        try {
            this.userNotificationElement = document.createElement('div');
            this.userNotificationElement.id = 'vsc-error-notification';
            this.userNotificationElement.style.cssText = `
                position: fixed;
                top: 60px;
                left: 20px;
                background-color: rgba(220, 53, 69, 0.9);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                font-weight: 500;
                z-index: ${UI_CONFIG.Z_INDEX.INDICATOR - 1};
                opacity: 0;
                transform: translateX(-100%);
                transition: all 0.3s ease-in-out;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                pointer-events: none;
                display: none;
            `;

            // 确保在body加载后添加
            if (document.body) {
                document.body.appendChild(this.userNotificationElement);
            } else {
                document.addEventListener('DOMContentLoaded', () => {
                    document.body.appendChild(this.userNotificationElement);
                });
            }
        } catch (error) {
            console.error('创建错误通知元素失败:', error);
        }
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 监听未捕获的错误
        window.addEventListener('error', (event) => {
            this.handleError('全局错误', event.error, {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });

        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError('未处理的Promise拒绝', event.reason);
        });
    }

    /**
     * 处理错误
     * @param {string} context - 错误上下文
     * @param {Error|string} error - 错误对象或消息
     * @param {Object} metadata - 附加元数据
     * @param {boolean} showToUser - 是否向用户显示
     * @param {string} severity - 错误严重程度
     * @param {string} type - 错误类型
     */
    handleError(context, error, metadata = {}, showToUser = false, severity = ERROR_SEVERITY.MEDIUM, type = ERROR_TYPES.RUNTIME) {
        this.errorCount++;

        const errorInfo = {
            id: generateUniqueId('error'),
            timestamp: new Date().toISOString(),
            context,
            severity,
            type,
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : { message: String(error) },
            metadata,
            userAgent: navigator.userAgent,
            url: window.location.href,
            recoverable: this.isRecoverableError(context, error, type)
        };

        // 添加到错误历史
        this.addToHistory(errorInfo);

        // 记录到控制台（避免循环依赖）
        try {
            logError(`[${context}] [${severity}] [${type}]`, error);
        } catch (logErr) {
            // 后备方案：直接使用console.error避免循环依赖
            console.error('[VSC ErrorHandler]', `[${context}] [${severity}] [${type}]`, error, logErr);
        }

        // 尝试错误恢复
        if (errorInfo.recoverable) {
            this.attemptErrorRecovery(context, error, type);
        }

        // 根据严重程度决定是否向用户显示
        const shouldShowToUser = showToUser || severity === ERROR_SEVERITY.HIGH || severity === ERROR_SEVERITY.CRITICAL;
        if (shouldShowToUser) {
            this.showUserNotification(context, error, severity);
        }

        // 在调试模式下，提供更详细的信息
        if (DEBUG_CONFIG.ENABLED) {
            console.group(`🔴 错误详情 - ${context}`);
            console.log('错误信息:', errorInfo);
            console.log('元数据:', metadata);
            console.log('错误历史:', this.errorHistory.slice(-5));
            console.groupEnd();
        }

        // 关键错误需要特殊处理
        if (severity === ERROR_SEVERITY.CRITICAL) {
            this.handleCriticalError(errorInfo);
        }
    }

    /**
     * 判断错误是否可恢复
     * @param {string} context - 错误上下文
     * @param {Error|string} error - 错误对象
     * @param {string} type - 错误类型
     * @returns {boolean} 是否可恢复
     */
    isRecoverableError(context, error, type) {
        // 初始化错误通常不可恢复
        if (type === ERROR_TYPES.INITIALIZATION) {
            return false;
        }

        // 权限错误通常不可恢复
        if (type === ERROR_TYPES.PERMISSION) {
            return false;
        }

        // 兼容性错误通常不可恢复
        if (type === ERROR_TYPES.COMPATIBILITY) {
            return false;
        }

        // 运行时错误和用户输入错误通常可恢复
        return type === ERROR_TYPES.RUNTIME || type === ERROR_TYPES.USER_INPUT;
    }

    /**
     * 尝试错误恢复
     * @param {string} context - 错误上下文
     * @param {Error|string} error - 错误对象
     * @param {string} type - 错误类型
     */
    attemptErrorRecovery(context, error, type) {
        try {
            switch (context) {
                case ERROR_MESSAGES.SHORTCUT_LOAD_FAILED:
                    // 重新加载默认快捷键
                    this.recoverShortcutSettings();
                    break;

                case ERROR_MESSAGES.MEDIA_DETECTION_FAILED:
                    // 重新初始化媒体检测
                    this.recoverMediaDetection();
                    break;

                case ERROR_MESSAGES.CACHE_OPERATION_FAILED:
                    // 清理缓存
                    this.recoverCache();
                    break;

                default:
                    try {
                        logWarn('没有为此错误类型定义恢复策略', { context, type });
                    } catch (logErr) {
                        console.warn('[VSC ErrorHandler]', '没有为此错误类型定义恢复策略', { context, type }, logErr);
                    }
            }
        } catch (recoveryError) {
            try {
                logError('错误恢复失败', recoveryError);
            } catch (logErr) {
                console.error('[VSC ErrorHandler]', '错误恢复失败', recoveryError, logErr);
            }
        }
    }

    /**
     * 恢复快捷键设置
     */
    recoverShortcutSettings() {
        // 这里应该调用ShortcutManager的恢复方法
        // 由于模块间的依赖关系，我们通过事件通知
        document.dispatchEvent(new CustomEvent('vsc-recover-shortcuts'));
    }

    /**
     * 恢复媒体检测
     */
    recoverMediaDetection() {
        document.dispatchEvent(new CustomEvent('vsc-recover-media-detection'));
    }

    /**
     * 恢复缓存
     */
    recoverCache() {
        document.dispatchEvent(new CustomEvent('vsc-recover-cache'));
    }

    /**
     * 处理关键错误
     * @param {Object} errorInfo - 错误信息
     */
    handleCriticalError(errorInfo) {
        // 关键错误需要记录更多信息
        console.error('🚨 关键错误发生:', errorInfo);

        // 可以考虑禁用扩展或重启
        document.dispatchEvent(new CustomEvent('vsc-critical-error', {
            detail: errorInfo
        }));
    }

    /**
     * 添加错误到历史记录
     * @param {Object} errorInfo - 错误信息
     */
    addToHistory(errorInfo) {
        this.errorHistory.push(errorInfo);

        // 限制历史记录大小
        if (this.errorHistory.length > this.maxHistorySize) {
            this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
        }
    }

    /**
     * 向用户显示错误通知
     * @param {string} context - 错误上下文
     * @param {Error|string} error - 错误对象或消息
     * @param {string} severity - 错误严重程度
     */
    showUserNotification(context, error, severity = ERROR_SEVERITY.MEDIUM) {
        if (!this.userNotificationElement) return;

        try {
            const message = this.getUserFriendlyMessage(context, error);
            this.userNotificationElement.textContent = message;

            // 根据严重程度设置样式
            this.updateNotificationStyle(severity);

            this.userNotificationElement.style.display = 'block';

            // 显示动画
            setTimeout(() => {
                this.userNotificationElement.style.opacity = '1';
                this.userNotificationElement.style.transform = 'translateX(0)';
            }, 10);

            // 根据严重程度设置自动隐藏时间
            const hideDelay = this.getHideDelay(severity);
            setTimeout(() => {
                this.hideUserNotification();
            }, hideDelay);
        } catch (err) {
            console.error('显示用户通知失败:', err);
        }
    }

    /**
     * 更新通知样式
     * @param {string} severity - 错误严重程度
     */
    updateNotificationStyle(severity) {
        const colors = {
            [ERROR_SEVERITY.LOW]: 'rgba(40, 167, 69, 0.9)',      // 绿色
            [ERROR_SEVERITY.MEDIUM]: 'rgba(255, 193, 7, 0.9)',   // 黄色
            [ERROR_SEVERITY.HIGH]: 'rgba(220, 53, 69, 0.9)',     // 红色
            [ERROR_SEVERITY.CRITICAL]: 'rgba(108, 117, 125, 0.9)' // 深灰色
        };

        const borderColors = {
            [ERROR_SEVERITY.LOW]: '#28a745',
            [ERROR_SEVERITY.MEDIUM]: '#ffc107',
            [ERROR_SEVERITY.HIGH]: '#dc3545',
            [ERROR_SEVERITY.CRITICAL]: '#6c757d'
        };

        this.userNotificationElement.style.backgroundColor = colors[severity] || colors[ERROR_SEVERITY.MEDIUM];
        this.userNotificationElement.style.borderLeftColor = borderColors[severity] || borderColors[ERROR_SEVERITY.MEDIUM];
    }

    /**
     * 获取隐藏延迟时间
     * @param {string} severity - 错误严重程度
     * @returns {number} 延迟时间（毫秒）
     */
    getHideDelay(severity) {
        const delays = {
            [ERROR_SEVERITY.LOW]: 3000,
            [ERROR_SEVERITY.MEDIUM]: 4000,
            [ERROR_SEVERITY.HIGH]: 6000,
            [ERROR_SEVERITY.CRITICAL]: 8000
        };

        return delays[severity] || delays[ERROR_SEVERITY.MEDIUM];
    }

    /**
     * 隐藏用户通知
     */
    hideUserNotification() {
        if (!this.userNotificationElement) return;

        try {
            this.userNotificationElement.style.opacity = '0';
            this.userNotificationElement.style.transform = 'translateX(-100%)';

            setTimeout(() => {
                this.userNotificationElement.style.display = 'none';
            }, 300);
        } catch (error) {
            console.error('隐藏用户通知失败:', error);
        }
    }

    /**
     * 获取用户友好的错误消息
     * @param {string} context - 错误上下文
     * @param {Error|string} error - 错误对象或消息
     * @returns {string} 用户友好的消息
     */
    getUserFriendlyMessage(context, error) {
        // 根据上下文返回用户友好的消息
        const contextMessages = {
            '加载快捷键失败': '快捷键设置加载失败，将使用默认设置',
            '处理快捷键变更失败': '快捷键更新失败，请重新设置',
            '处理键盘事件失败': '快捷键响应异常，请刷新页面',
            '检查媒体元素失败': '视频检测异常，部分功能可能不可用',
            '播放控制失败': '视频控制失败，请检查视频是否可用',
            '显示指示器失败': '状态显示异常',
            '切换全屏模式失败': '全屏模式切换失败'
        };

        return contextMessages[context] || `操作失败: ${context}`;
    }

    /**
     * 获取错误统计信息
     * @returns {Object} 错误统计
     */
    getErrorStats() {
        return {
            totalErrors: this.errorCount,
            recentErrors: this.errorHistory.slice(-10),
            errorsByContext: this.getErrorsByContext()
        };
    }

    /**
     * 按上下文分组错误
     * @returns {Object} 按上下文分组的错误统计
     */
    getErrorsByContext() {
        const contextCounts = {};
        this.errorHistory.forEach(error => {
            const context = error.context;
            contextCounts[context] = (contextCounts[context] || 0) + 1;
        });
        return contextCounts;
    }

    /**
     * 清除错误历史
     */
    clearHistory() {
        this.errorHistory = [];
        this.errorCount = 0;
    }

    /**
     * 创建错误包装器函数
     * @param {Function} func - 要包装的函数
     * @param {string} context - 错误上下文
     * @param {boolean} showToUser - 是否向用户显示错误
     * @param {string} severity - 错误严重程度
     * @param {string} type - 错误类型
     * @returns {Function} 包装后的函数
     */
    wrapFunction(func, context, showToUser = false, severity = ERROR_SEVERITY.MEDIUM, type = ERROR_TYPES.RUNTIME) {
        return (...args) => {
            try {
                return func(...args);
            } catch (error) {
                this.handleError(context, error, { args }, showToUser, severity, type);
                return null;
            }
        };
    }

    /**
     * 创建异步错误包装器函数
     * @param {Function} asyncFunc - 要包装的异步函数
     * @param {string} context - 错误上下文
     * @param {boolean} showToUser - 是否向用户显示错误
     * @param {string} severity - 错误严重程度
     * @param {string} type - 错误类型
     * @returns {Function} 包装后的异步函数
     */
    wrapAsyncFunction(asyncFunc, context, showToUser = false, severity = ERROR_SEVERITY.MEDIUM, type = ERROR_TYPES.RUNTIME) {
        return async (...args) => {
            try {
                return await asyncFunc(...args);
            } catch (error) {
                this.handleError(context, error, { args }, showToUser, severity, type);
                return null;
            }
        };
    }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler();

// 导出便捷函数
export const handleError = (context, error, metadata, showToUser, severity, type) =>
    errorHandler.handleError(context, error, metadata, showToUser, severity, type);

export const wrapFunction = (func, context, showToUser, severity, type) =>
    errorHandler.wrapFunction(func, context, showToUser, severity, type);

export const wrapAsyncFunction = (asyncFunc, context, showToUser, severity, type) =>
    errorHandler.wrapAsyncFunction(asyncFunc, context, showToUser, severity, type);

export const getErrorStats = () => errorHandler.getErrorStats();

export const clearErrorHistory = () => errorHandler.clearHistory();

export default errorHandler;
